const express = require('express');
const multer = require('multer');
const ffmpeg = require('fluent-ffmpeg');
const path = require('path');
const fs = require('fs');
const cors = require('cors');

const app = express();
const PORT = 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));
app.use('/uploads', express.static('uploads'));

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/');
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, 'video-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ 
  storage: storage,
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('video/')) {
      cb(null, true);
    } else {
      cb(new Error('Only video files are allowed!'), false);
    }
  }
});

// Routes
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Upload and get video info
app.post('/api/upload', upload.single('video'), (req, res) => {
  if (!req.file) {
    return res.status(400).json({ error: 'No video file uploaded' });
  }

  const videoPath = req.file.path;
  
  // Get video dimensions
  ffmpeg.ffprobe(videoPath, (err, metadata) => {
    if (err) {
      console.error('Error getting video info:', err);
      return res.status(500).json({ error: 'Failed to process video' });
    }

    const videoStream = metadata.streams.find(stream => stream.codec_type === 'video');
    const dimensions = {
      width: videoStream.width,
      height: videoStream.height
    };

    res.json({
      success: true,
      filename: req.file.filename,
      dimensions: dimensions
    });
  });
});

// Crop video
app.post('/api/crop', (req, res) => {
  const { filename, cropData, doctorName } = req.body;

  if (!filename || !cropData || !doctorName) {
    return res.status(400).json({ error: 'Missing required data' });
  }

  const inputPath = path.join('uploads', filename);
  const croppedFilename = 'cropped-' + Date.now() + '.mp4';
  const croppedPath = path.join('uploads', croppedFilename);

  // Ensure crop values are integers and within bounds
  const x = Math.max(0, Math.floor(cropData.x));
  const y = Math.max(0, Math.floor(cropData.y));
  const width = Math.max(50, Math.floor(cropData.width));
  const height = Math.max(50, Math.floor(cropData.height));

  console.log(`Applying crop: ${width}x${height} at (${x},${y})`);

  // Crop the video with improved settings
  ffmpeg(inputPath)
    .videoFilters(`crop=${width}:${height}:${x}:${y}`)
    .videoCodec('libx264')
    .audioCodec('aac')
    .outputOptions([
      '-preset fast',
      '-crf 23',
      '-pix_fmt yuv420p'
    ])
    .output(croppedPath)
    .on('end', () => {
      console.log('Video cropped successfully');

      // Schedule cleanup of original file after 5 minutes
      setTimeout(() => {
        fs.unlink(inputPath, (err) => {
          if (err) console.error('Error deleting original file:', err);
          else console.log('Cleaned up original file:', filename);
        });
      }, 5 * 60 * 1000);

      // Now overlay the cropped video on the background video
      overlayVideo(croppedFilename, doctorName, res);
    })
    .on('error', (err) => {
      console.error('Crop error:', err);
      res.status(500).json({ error: 'Failed to crop video: ' + err.message });
    })
    .run();
});

// Function to overlay cropped video on background video
function overlayVideo(croppedFilename, doctorName, res) {
  const backgroundVideo = path.join('uploads', 'Gudi padwa GIF.mp4');
  const croppedVideo = path.join('uploads', croppedFilename);
  const finalFilename = 'final-' + Date.now() + '.mp4';
  const finalPath = path.join('uploads', finalFilename);
  
  // Fixed position for overlay (you can modify these values)
  const overlayX = 100; // X position
  const overlayY = 100; // Y position
  const overlayDelay = 2; // Start overlay after 2 seconds

  console.log(`Overlaying video at position (${overlayX}, ${overlayY}) with ${overlayDelay}s delay`);

  ffmpeg(backgroundVideo)
    .input(croppedVideo)
    .complexFilter([
      `[1:v]setpts=PTS+${overlayDelay}/TB[delayed]`,
      `[0:v][delayed]overlay=${overlayX}:${overlayY}:enable='gte(t,${overlayDelay})'[v]`
    ])
    .outputOptions([
      '-map', '[v]',
      '-map', '0:a?',
      '-c:v', 'libx264',
      '-c:a', 'aac',
      '-preset', 'fast',
      '-crf', '23',
      '-pix_fmt', 'yuv420p'
    ])
    .output(finalPath)
    .on('end', () => {
      console.log('Final video created successfully');

      // Schedule cleanup of cropped video after 5 minutes
      setTimeout(() => {
        fs.unlink(croppedVideo, (err) => {
          if (err) console.error('Error deleting cropped video:', err);
          else console.log('Cleaned up cropped file:', croppedFilename);
        });
      }, 5 * 60 * 1000);

      // Schedule cleanup of final video after 5 minutes
      setTimeout(() => {
        fs.unlink(finalPath, (err) => {
          if (err) console.error('Error deleting final video:', err);
          else console.log('Cleaned up final file:', finalFilename);
        });
      }, 5 * 60 * 1000);

      res.json({
        success: true,
        finalVideo: finalFilename,
        doctorName: doctorName,
        message: 'Video processed successfully'
      });
    })
    .on('error', (err) => {
      console.error('Overlay error:', err);
      res.status(500).json({ error: 'Failed to create final video: ' + err.message });
    })
    .run();
}

// Download final video
app.get('/api/download/:filename', (req, res) => {
  const filename = req.params.filename;
  const filePath = path.join(__dirname, 'uploads', filename);
  
  if (fs.existsSync(filePath)) {
    res.download(filePath, `${filename}`, (err) => {
      if (err) {
        console.error('Download error:', err);
        res.status(500).json({ error: 'Failed to download file' });
      }
    });
  } else {
    res.status(404).json({ error: 'File not found' });
  }
});

app.listen(PORT, () => {
  console.log(`🚀 Server running at http://localhost:${PORT}`);
  console.log('📁 Make sure "Gudi padwa GIF.mp4" is in the uploads folder');
});
