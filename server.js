const express = require('express');
const multer = require('multer');
const ffmpeg = require('fluent-ffmpeg');
const path = require('path');
const fs = require('fs');
const cors = require('cors');

const app = express();
const PORT = 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));
app.use('/uploads', express.static('uploads'));

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/');
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, 'video-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ 
  storage: storage,
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('video/')) {
      cb(null, true);
    } else {
      cb(new Error('Only video files are allowed!'), false);
    }
  }
});

// Upload video
app.post('/api/upload', upload.single('video'), (req, res) => {
  if (!req.file) {
    return res.status(400).json({ error: 'No video file uploaded' });
  }

  const videoPath = req.file.path;
  
  // Get video dimensions
  ffmpeg.ffprobe(videoPath, (err, metadata) => {
    if (err) {
      console.error('Error getting video info:', err);
      return res.status(500).json({ error: 'Failed to process video' });
    }

    const videoStream = metadata.streams.find(stream => stream.codec_type === 'video');
    const dimensions = {
      width: videoStream.width,
      height: videoStream.height
    };

    console.log('Video uploaded:', req.file.filename, 'Dimensions:', dimensions);

    res.json({
      success: true,
      filename: req.file.filename,
      dimensions: dimensions
    });
  });
});

// Crop and overlay video
app.post('/api/crop', (req, res) => {
  const { filename, cropData, doctorName } = req.body;
  
  if (!filename || !cropData || !doctorName) {
    return res.status(400).json({ error: 'Missing required data' });
  }

  console.log('Crop request:', { filename, cropData, doctorName });

  const inputPath = path.join('uploads', filename);
  const croppedFilename = 'cropped-' + Date.now() + '.mp4';
  const croppedPath = path.join('uploads', croppedFilename);
  
  // Ensure crop values are valid integers
  const x = Math.max(0, Math.floor(Number(cropData.x) || 0));
  const y = Math.max(0, Math.floor(Number(cropData.y) || 0));
  const width = Math.max(50, Math.floor(Number(cropData.width) || 100));
  const height = Math.max(50, Math.floor(Number(cropData.height) || 100));

  console.log(`Cropping: ${width}x${height} at (${x},${y})`);
  
  // Step 1: Crop the video
  ffmpeg(inputPath)
    .videoFilters(`crop=${width}:${height}:${x}:${y}`)
    .videoCodec('libx264')
    .audioCodec('aac')
    .outputOptions(['-preset', 'fast', '-crf', '23'])
    .output(croppedPath)
    .on('end', () => {
      console.log('Video cropped successfully');
      
      // Step 2: Overlay on background video
      const backgroundVideo = path.join('uploads', 'Gudi padwa GIF.mp4');
      const finalFilename = 'final-' + Date.now() + '.mp4';
      const finalPath = path.join('uploads', finalFilename);
      
      // Check if background video exists
      if (!fs.existsSync(backgroundVideo)) {
        return res.status(500).json({ error: 'Background video not found' });
      }
      
      // Overlay cropped video on background (show from 2s to 6s)
      ffmpeg(backgroundVideo)
        .input(croppedPath)
        .complexFilter([
          '[1:v]setpts=PTS+2/TB[delayed]',
          '[0:v][delayed]overlay=100:100:enable=\'between(t,2,6)\'[v]'
        ])
        .outputOptions(['-map', '[v]', '-map', '0:a?'])
        .videoCodec('libx264')
        .audioCodec('aac')
        .outputOptions(['-preset', 'fast', '-crf', '23'])
        .output(finalPath)
        .on('end', () => {
          console.log('Final video created successfully');
          
          // Clean up files after 5 minutes
          setTimeout(() => {
            [inputPath, croppedPath, finalPath].forEach(filePath => {
              fs.unlink(filePath, (err) => {
                if (err) console.error('Cleanup error:', err);
                else console.log('Cleaned up:', path.basename(filePath));
              });
            });
          }, 5 * 60 * 1000);
          
          res.json({
            success: true,
            finalVideo: finalFilename,
            message: 'Video processed successfully'
          });
        })
        .on('error', (err) => {
          console.error('Overlay error:', err);
          res.status(500).json({ error: 'Failed to create final video' });
        })
        .run();
    })
    .on('error', (err) => {
      console.error('Crop error:', err);
      res.status(500).json({ error: 'Failed to crop video' });
    })
    .run();
});

// Download video
app.get('/api/download/:filename', (req, res) => {
  const filename = req.params.filename;
  const filePath = path.join('uploads', filename);
  
  if (fs.existsSync(filePath)) {
    res.download(filePath);
  } else {
    res.status(404).json({ error: 'File not found' });
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Server running at http://localhost:${PORT}`);
  console.log('📁 Make sure "Gudi padwa GIF.mp4" is in the uploads folder');
});
