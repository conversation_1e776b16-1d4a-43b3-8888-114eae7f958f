html
{
    overflow: auto;
    background-color: #fff;
}

body
{
    font: 14px "DejaVu Sans Condensed", "Liberation Sans", "Nimbus Sans L", Tahoma, Geneva, "Helvetica Neue", Helvetica, Arial, sans-serif;
    line-height: 130%;
    color: #000;
    background-color: #fff;
}

a {
    color: #444;
}

a:visited {
    color: #444;
}

a:active {
    color: #444;
}

header
{
    display: block;
    padding: 6px 4px;
}

.class-description {
    font-style: italic;
    font-family: <PERSON><PERSON>ino, 'Palatino Linotype', serif;
    font-size: 130%;
    line-height: 140%;
    margin-bottom: 1em;
    margin-top: 1em;
}

#main {
    float: left;
    width: 100%;
}

section
{
    display: block;

    background-color: #fff;
    padding: 12px 24px;
    border-bottom: 1px solid #ccc;
    margin-right: 240px;
}

.variation {
    display: none;
}

.optional:after {
    content: "opt";
    font-size: 60%;
    color: #aaa;
    font-style: italic;
    font-weight: lighter;
}

nav
{
    display: block;
    width: 220px;
    border-left: 1px solid #ccc;
    padding-left: 9px;

    position: fixed;
    top: 28px;
    right: 0;
}

nav ul {
    font-family: 'Lucida Grande', 'Lucida Sans Unicode', arial, sans-serif;
    font-size: 100%;
    line-height: 17px;
    padding:0;
    margin:0;
    list-style-type:none;
}

nav ul ul {
    margin-left: 10px;
    font-size: 90%;
}

nav h2 a, nav h2 a:visited {
    color: #526492;
    text-decoration: none;
}

nav h3 {
    margin-top: 12px;
}

nav li {
    margin-top: 2px;
}

nav a {
    color: #5C5954;
}

nav a:visited {
    color: #5C5954;
}

nav a:active {
    color: #5C5954;
}

footer {
    display: block;
    padding: 6px;
    margin-top: 12px;
    font-style: italic;
    font-size: 90%;
}

h1
{
    font-size: 200%;
    font-weight: bold;
    letter-spacing: -0.01em;
    margin: 6px 0 9px 0;
}

h2
{
    font-size: 170%;
    font-weight: bold;
    letter-spacing: -0.01em;
    margin: 50px 0 3px 0;
}

nav > h2 {
    margin-top: 6px;
}

h3
{
    font-size: 150%;
    font-weight: bold;
    letter-spacing: -0.01em;
    margin-top: 16px;
    margin: 50px 0 3px 0;
}

h4
{
    font-size: 130%;
    font-weight: bold;
    letter-spacing: -0.01em;
    margin-top: 16px;
    margin: 18px 0 3px 0;
    color: #526492;
}

h5, .container-overview .subsection-title
{
    font-size: 120%;
    font-weight: bold;
    letter-spacing: -0.01em;
    margin: 8px 0 3px -16px;
}

h6
{
    font-size: 100%;
    letter-spacing: -0.01em;
    margin: 6px 0 3px 0;
    font-style: italic;
}

article > dl, article > pre {
    margin-left: 2em;
}

.ancestors { color: #999; }
.ancestors a
{
    color: #999 !important;
    text-decoration: none;
}

.important
{
    font-weight: bold;
    color: #950B02;
}

.yes-def {
    text-indent: -1000px;
}

.type-signature {
    color: #aaa;
}

.name, .signature {
    font-family: Consolas, "Lucida Console", Monaco, monospace;
}

.details { margin-top: 14px; border-left: 2px solid #DDD; }
.details dt { width:100px; float:left; padding-left: 10px;  padding-top: 6px; }
.details dd { margin-left: 50px; }
.details ul { margin: 0; }
.details ul { list-style-type: none; }
.details li { margin-left: 30px; padding-top: 6px; }
.details pre.prettyprint { margin: 0 }
.details .object-value { padding-top: 0; }

.description {
    margin-bottom: 1em;
    margin-left: -16px;
    margin-top: 1em;
}

.code-caption
{
    font-style: italic;
    font-family: Palatino, 'Palatino Linotype', serif;
    font-size: 107%;
    margin: 0;
}

.prettyprint
{
    border: 1px solid #ddd;
    width: 80%;
    overflow: auto;
}

.prettyprint.source {
    width: inherit;
}

.prettyprint code
{
    font-family: Consolas, 'Lucida Console', Monaco, monospace;
    font-size: 100%;
    line-height: 18px;
    display: block;
    padding: 4px 12px;
    margin: 0;
    background-color: #fff;
    color: #000;
}

.prettyprint code span.line
{
  display: inline-block;
}

.prettyprint.linenums
{
  padding-left: 70px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.prettyprint.linenums ol
{
  padding-left: 0;
}

.prettyprint.linenums li
{
  border-left: 3px #ddd solid;
}

.prettyprint.linenums li.selected,
.prettyprint.linenums li.selected *
{
  background-color: lightyellow;
}

.prettyprint.linenums li *
{
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

.params, .props
{
    border-spacing: 0;
    border: 0;
    border-collapse: collapse;
}

.params .name, .props .name, .name code {
    color: #526492;
    font-family: Consolas, 'Lucida Console', Monaco, monospace;
    font-size: 100%;
}

.params td, .params th, .props td, .props th
{
    border: 1px solid #ddd;
    margin: 0px;
    text-align: left;
    vertical-align: top;
    padding: 4px 6px;
    display: table-cell;
}

.params thead tr, .props thead tr
{
    background-color: #ddd;
    font-weight: bold;
}

.params .params thead tr, .props .props thead tr
{
    background-color: #fff;
    font-weight: bold;
}

.params th, .props th { border-right: 1px solid #aaa; }
.params thead .last, .props thead .last { border-right: 1px solid #ddd; }

.params td.description > p:first-child
{
    margin-top: 0;
    padding-top: 0;
}

.params td.description > p:last-child
{
    margin-bottom: 0;
    padding-bottom: 0;
}

.disabled {
    color: #454545;
}
