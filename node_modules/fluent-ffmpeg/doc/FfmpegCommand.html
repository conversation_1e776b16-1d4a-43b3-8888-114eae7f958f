<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>JSDoc: Class: FfmpegCommand</title>

    <script src="scripts/prettify/prettify.js"> </script>
    <script src="scripts/prettify/lang-css.js"> </script>
    <!--[if lt IE 9]>
      <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <link type="text/css" rel="stylesheet" href="styles/prettify-tomorrow.css">
    <link type="text/css" rel="stylesheet" href="styles/jsdoc-default.css">
</head>

<body>

<div id="main">

    <h1 class="page-title">Class: FfmpegCommand</h1>

    



<section>

<header>
    <h2>
    FfmpegCommand
    </h2>
    
</header>

<article>
    <div class="container-overview">
    

    
        
<dt>
    <h4 class="name" id="FfmpegCommand"><span class="type-signature"></span>new FfmpegCommand<span class="signature">(<span class="optional">input</span>, <span class="optional">options</span>)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Create an ffmpeg command</p>
<p>Can be called with or without the 'new' operator, and the 'input' parameter
may be specified as 'options.source' instead (or passed later with the
addInput method).</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>input</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">ReadableStream</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>input file path or readable stream</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>options</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>command options</p>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        
        <th>Default</th>
        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>logger</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                    &lt;no logging>
                
                </td>
            

            <td class="description last"><p>logger object with 'error', 'warning', 'info' and 'debug' methods</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>niceness</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                    0
                
                </td>
            

            <td class="description last"><p>ffmpeg process niceness, ignored on Windows</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>priority</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                    0
                
                </td>
            

            <td class="description last"><p>alias for <code>niceness</code></p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>presets</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                    "fluent-ffmpeg/lib/presets"
                
                </td>
            

            <td class="description last"><p>directory to load presets from</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>preset</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                    "fluent-ffmpeg/lib/presets"
                
                </td>
            

            <td class="description last"><p>alias for <code>presets</code></p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>stdoutLines</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                    100
                
                </td>
            

            <td class="description last"><p>maximum lines of ffmpeg output to keep in memory, use 0 for unlimited</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>timeout</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                    &lt;no timeout>
                
                </td>
            

            <td class="description last"><p>ffmpeg processing timeout in seconds</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>source</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">ReadableStream</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                    &lt;no input>
                
                </td>
            

            <td class="description last"><p>alias for the <code>input</code> parameter</p></td>
        </tr>

    
    </tbody>
</table>
            </td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="fluent-ffmpeg.js.html">fluent-ffmpeg.js</a>, <a href="fluent-ffmpeg.js.html#line31">line 31</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

    
    </div>

    

    

    

    

    

    

    
        <h3 class="subsection-title"><a name="audio-methods"></a>Audio methods</h3>

        <dl>
            
<dt>
    <h4 class="name" id="audioBitrate"><span class="type-signature"></span>audioBitrate<span class="signature">(bitrate)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Specify audio bitrate</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>bitrate</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last"><p>audio bitrate in kbps (with an optional 'k' suffix)</p></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="options_audio.js.html">options/audio.js</a>, <a href="options_audio.js.html#line48">line 48</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>FfmpegCommand</p>
</div>



        

    

    
        <h5>Alias:</h5>
        

	withAudioBitrate

    
</dd>

        
            
<dt>
    <h4 class="name" id="audioChannels"><span class="type-signature"></span>audioChannels<span class="signature">(channels)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Specify audio channel count</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>channels</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last"><p>channel count</p></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="options_audio.js.html">options/audio.js</a>, <a href="options_audio.js.html#line65">line 65</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>FfmpegCommand</p>
</div>



        

    

    
        <h5>Alias:</h5>
        

	withAudioChannels

    
</dd>

        
            
<dt>
    <h4 class="name" id="audioCodec"><span class="type-signature"></span>audioCodec<span class="signature">(codec)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Specify audio codec</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>codec</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            

            

            <td class="description last"><p>audio codec name</p></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="options_audio.js.html">options/audio.js</a>, <a href="options_audio.js.html#line30">line 30</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>FfmpegCommand</p>
</div>



        

    

    
        <h5>Alias:</h5>
        

	withAudioCodec

    
</dd>

        
            
<dt>
    <h4 class="name" id="audioFilter"><span class="type-signature"></span>audioFilter<span class="signature">(filters)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#audioFilters">FfmpegCommand#audioFilters</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="audioFilters"><span class="type-signature"></span>audioFilters<span class="signature">(filters)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Specify custom audio filter(s)</p>
<p>Can be called both with one or many filters, or a filter array.</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>filters</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">Array.&lt;String></span>
|

<span class="param-type">Array.&lt;Object></span>


            
            </td>

            

            

            <td class="description last"><p>audio filter strings, string array or
  filter specification array, each with the following properties:</p>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>filter</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>filter name</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>options</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">Array.&lt;String></span>
|

<span class="param-type">Object</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>filter option string, array, or object</p></td>
        </tr>

    
    </tbody>
</table>
            </td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="options_audio.js.html">options/audio.js</a>, <a href="options_audio.js.html#line116">line 116</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>FfmpegCommand</p>
</div>



        

    
        <h5>Examples:</h5>
        
    <pre class="prettyprint"><code>command.audioFilters('filter1');</code></pre>

    <pre class="prettyprint"><code>command.audioFilters('filter1', 'filter2=param1=value1:param2=value2');</code></pre>

    <pre class="prettyprint"><code>command.audioFilters(['filter1', 'filter2']);</code></pre>

    <pre class="prettyprint"><code>command.audioFilters([
  {
    filter: 'filter1'
  },
  {
    filter: 'filter2',
    options: 'param=value:param=value'
  }
]);</code></pre>

    <pre class="prettyprint"><code>command.audioFilters(
  {
    filter: 'filter1',
    options: ['value1', 'value2']
  },
  {
    filter: 'filter2',
    options: { param1: 'value1', param2: 'value2' }
  }
);</code></pre>

    

    
        <h5>Aliases:</h5>
        

<ul>
	
		<li>withAudioFilter</li>
	
		<li>withAudioFilters</li>
	
		<li>audioFilter</li>
	
</ul>

    
</dd>

        
            
<dt>
    <h4 class="name" id="audioFrequency"><span class="type-signature"></span>audioFrequency<span class="signature">(freq)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Specify audio frequency</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>freq</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last"><p>audio frequency in Hz</p></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="options_audio.js.html">options/audio.js</a>, <a href="options_audio.js.html#line82">line 82</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>FfmpegCommand</p>
</div>



        

    

    
        <h5>Alias:</h5>
        

	withAudioFrequency

    
</dd>

        
            
<dt>
    <h4 class="name" id="audioQuality"><span class="type-signature"></span>audioQuality<span class="signature">(quality)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Specify audio quality</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>quality</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last"><p>audio quality factor</p></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="options_audio.js.html">options/audio.js</a>, <a href="options_audio.js.html#line99">line 99</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>FfmpegCommand</p>
</div>



        

    

    
        <h5>Alias:</h5>
        

	withAudioQuality

    
</dd>

        
            
<dt>
    <h4 class="name" id="noAudio"><span class="type-signature"></span>noAudio<span class="signature">()</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Disable audio in the output</p>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="options_audio.js.html">options/audio.js</a>, <a href="options_audio.js.html#line12">line 12</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>FfmpegCommand</p>
</div>



        

    

    
        <h5>Alias:</h5>
        

	withNoAudio

    
</dd>

        
            
<dt>
    <h4 class="name" id="withAudioBitrate"><span class="type-signature"></span>withAudioBitrate<span class="signature">(bitrate)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#audioBitrate">FfmpegCommand#audioBitrate</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="withAudioChannels"><span class="type-signature"></span>withAudioChannels<span class="signature">(channels)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#audioChannels">FfmpegCommand#audioChannels</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="withAudioCodec"><span class="type-signature"></span>withAudioCodec<span class="signature">(codec)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#audioCodec">FfmpegCommand#audioCodec</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="withAudioFilter"><span class="type-signature"></span>withAudioFilter<span class="signature">(filters)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#audioFilters">FfmpegCommand#audioFilters</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="withAudioFilters"><span class="type-signature"></span>withAudioFilters<span class="signature">(filters)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#audioFilters">FfmpegCommand#audioFilters</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="withAudioFrequency"><span class="type-signature"></span>withAudioFrequency<span class="signature">(freq)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#audioFrequency">FfmpegCommand#audioFrequency</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="withAudioQuality"><span class="type-signature"></span>withAudioQuality<span class="signature">(quality)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#audioQuality">FfmpegCommand#audioQuality</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="withNoAudio"><span class="type-signature"></span>withNoAudio<span class="signature">()</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#noAudio">FfmpegCommand#noAudio</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        </dl>
    
        <h3 class="subsection-title"><a name="capabilities-methods"></a>Capabilities methods</h3>

        <dl>
            
<dt>
    <h4 class="name" id="availableCodecs"><span class="type-signature"></span>availableCodecs<span class="signature">(callback)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Query ffmpeg for available codecs</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>callback</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="FfmpegCommand.html#~codecCallback">FfmpegCommand~codecCallback</a></span>


            
            </td>

            

            

            <td class="description last"><p>callback function</p></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="capabilities.js.html">capabilities.js</a>, <a href="capabilities.js.html#line343">line 343</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    

    

    
        <h5>Alias:</h5>
        

	getAvailableCodecs

    
</dd>

        
            
<dt>
    <h4 class="name" id="availableEncoders"><span class="type-signature"></span>availableEncoders<span class="signature">(callback)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Query ffmpeg for available encoders</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>callback</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="FfmpegCommand.html#~encodersCallback">FfmpegCommand~encodersCallback</a></span>


            
            </td>

            

            

            <td class="description last"><p>callback function</p></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="capabilities.js.html">capabilities.js</a>, <a href="capabilities.js.html#line445">line 445</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    

    

    
        <h5>Alias:</h5>
        

	getAvailableEncoders

    
</dd>

        
            
<dt>
    <h4 class="name" id="availableFilters"><span class="type-signature"></span>availableFilters<span class="signature">(callback)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Query ffmpeg for available filters</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>callback</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="FfmpegCommand.html#~filterCallback">FfmpegCommand~filterCallback</a></span>


            
            </td>

            

            

            <td class="description last"><p>callback function</p></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="capabilities.js.html">capabilities.js</a>, <a href="capabilities.js.html#line287">line 287</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    

    

    
        <h5>Alias:</h5>
        

	getAvailableFilters

    
</dd>

        
            
<dt>
    <h4 class="name" id="availableFormats"><span class="type-signature"></span>availableFormats<span class="signature">(callback)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Query ffmpeg for available formats</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>callback</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="FfmpegCommand.html#~formatCallback">FfmpegCommand~formatCallback</a></span>


            
            </td>

            

            

            <td class="description last"><p>callback function</p></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="capabilities.js.html">capabilities.js</a>, <a href="capabilities.js.html#line501">line 501</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    

    

    
        <h5>Alias:</h5>
        

	getAvailableFormats

    
</dd>

        
            
<dt>
    <h4 class="name" id="getAvailableCodecs"><span class="type-signature"></span>getAvailableCodecs<span class="signature">(callback)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#availableCodecs">FfmpegCommand#availableCodecs</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="getAvailableEncoders"><span class="type-signature"></span>getAvailableEncoders<span class="signature">(callback)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#availableEncoders">FfmpegCommand#availableEncoders</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="getAvailableFilters"><span class="type-signature"></span>getAvailableFilters<span class="signature">(callback)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#availableFilters">FfmpegCommand#availableFilters</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="getAvailableFormats"><span class="type-signature"></span>getAvailableFormats<span class="signature">(callback)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#availableFormats">FfmpegCommand#availableFormats</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        </dl>
    
        <h3 class="subsection-title"><a name="custom-options-methods"></a>Custom options methods</h3>

        <dl>
            
<dt>
    <h4 class="name" id="addInputOption"><span class="type-signature"></span>addInputOption<span class="signature">(options)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#inputOptions">FfmpegCommand#inputOptions</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="addInputOptions"><span class="type-signature"></span>addInputOptions<span class="signature">(options)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#inputOptions">FfmpegCommand#inputOptions</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="addOption"><span class="type-signature"></span>addOption<span class="signature">(options)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#outputOptions">FfmpegCommand#outputOptions</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="addOptions"><span class="type-signature"></span>addOptions<span class="signature">(options)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#outputOptions">FfmpegCommand#outputOptions</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="addOutputOption"><span class="type-signature"></span>addOutputOption<span class="signature">(options)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#outputOptions">FfmpegCommand#outputOptions</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="addOutputOptions"><span class="type-signature"></span>addOutputOptions<span class="signature">(options)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#outputOptions">FfmpegCommand#outputOptions</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="complexFilter"><span class="type-signature"></span>complexFilter<span class="signature">(spec, <span class="optional">map</span>)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Specify a complex filtergraph</p>
<p>Calling this method will override any previously set filtergraph, but you can set
as many filters as needed in one call.</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>spec</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">Array</span>


            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>filtergraph string or array of filter specification
  objects, each having the following properties:</p>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>filter</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>filter name</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>inputs</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">Array</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>(array of) input stream specifier(s) for the filter,
  defaults to ffmpeg automatically choosing the first unused matching streams</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>outputs</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">Array</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>(array of) output stream specifier(s) for the filter,
  defaults to ffmpeg automatically assigning the output to the output file</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>options</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>
|

<span class="param-type">String</span>
|

<span class="param-type">Array</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>filter options, can be omitted to not set any options</p></td>
        </tr>

    
    </tbody>
</table>
            </td>
        </tr>

    

        <tr>
            
                <td class="name"><code>map</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>(array of) stream specifier(s) from the graph to include in
  ffmpeg output, defaults to ffmpeg automatically choosing the first matching streams.</p></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="options_custom.js.html">options/custom.js</a>, <a href="options_custom.js.html#line127">line 127</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>FfmpegCommand</p>
</div>



        

    
        <h5>Examples:</h5>
        
        <p class="code-caption">Overlay an image over a video (using a filtergraph string)</p>
    
    <pre class="prettyprint"><code>  ffmpeg()
    .input('video.avi')
    .input('image.png')
    .complexFilter('[0:v][1:v]overlay[out]', ['out']);</code></pre>

        <p class="code-caption">Overlay an image over a video (using a filter array)</p>
    
    <pre class="prettyprint"><code>  ffmpeg()
    .input('video.avi')
    .input('image.png')
    .complexFilter([{
      filter: 'overlay',
      inputs: ['0:v', '1:v'],
      outputs: ['out']
    }], ['out']);</code></pre>

        <p class="code-caption">Split video into RGB channels and output a 3x1 video with channels side to side</p>
    
    <pre class="prettyprint"><code> ffmpeg()
   .input('video.avi')
   .complexFilter([
     // Duplicate video stream 3 times into streams a, b, and c
     { filter: 'split', options: '3', outputs: ['a', 'b', 'c'] },

     // Create stream 'red' by cancelling green and blue channels from stream 'a'
     { filter: 'lutrgb', options: { g: 0, b: 0 }, inputs: 'a', outputs: 'red' },

     // Create stream 'green' by cancelling red and blue channels from stream 'b'
     { filter: 'lutrgb', options: { r: 0, b: 0 }, inputs: 'b', outputs: 'green' },

     // Create stream 'blue' by cancelling red and green channels from stream 'c'
     { filter: 'lutrgb', options: { r: 0, g: 0 }, inputs: 'c', outputs: 'blue' },

     // Pad stream 'red' to 3x width, keeping the video on the left, and name output 'padded'
     { filter: 'pad', options: { w: 'iw*3', h: 'ih' }, inputs: 'red', outputs: 'padded' },

     // Overlay 'green' onto 'padded', moving it to the center, and name output 'redgreen'
     { filter: 'overlay', options: { x: 'w', y: 0 }, inputs: ['padded', 'green'], outputs: 'redgreen'},

     // Overlay 'blue' onto 'redgreen', moving it to the right
     { filter: 'overlay', options: { x: '2*w', y: 0 }, inputs: ['redgreen', 'blue']},
   ]);</code></pre>

    

    
        <h5>Alias:</h5>
        

	filterGraph

    
</dd>

        
            
<dt>
    <h4 class="name" id="filterGraph"><span class="type-signature"></span>filterGraph<span class="signature">(spec, <span class="optional">map</span>)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#complexFilter">FfmpegCommand#complexFilter</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="inputOption"><span class="type-signature"></span>inputOption<span class="signature">(options)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#inputOptions">FfmpegCommand#inputOptions</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="inputOptions"><span class="type-signature"></span>inputOptions<span class="signature">(options)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Add custom input option(s)</p>
<p>When passing a single string or an array, each string containing two
words is split (eg. inputOptions('-option value') is supported) for
compatibility reasons.  This is not the case when passing more than
one argument.</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>options</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            
                <td class="attributes">
                

                

                
                    &lt;repeatable><br>
                
                </td>
            

            

            <td class="description last"><p>option string(s) or string array</p></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="options_custom.js.html">options/custom.js</a>, <a href="options_custom.js.html#line12">line 12</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>FfmpegCommand</p>
</div>



        

    
        <h5>Examples:</h5>
        
    <pre class="prettyprint"><code>command.inputOptions('option1');</code></pre>

    <pre class="prettyprint"><code>command.inputOptions('option1', 'option2');</code></pre>

    <pre class="prettyprint"><code>command.inputOptions(['option1', 'option2']);</code></pre>

    

    
        <h5>Aliases:</h5>
        

<ul>
	
		<li>addInputOption</li>
	
		<li>addInputOptions</li>
	
		<li>withInputOption</li>
	
		<li>withInputOptions</li>
	
		<li>inputOption</li>
	
</ul>

    
</dd>

        
            
<dt>
    <h4 class="name" id="outputOption"><span class="type-signature"></span>outputOption<span class="signature">(options)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#outputOptions">FfmpegCommand#outputOptions</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="outputOptions"><span class="type-signature"></span>outputOptions<span class="signature">(options)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Add custom output option(s)</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>options</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            
                <td class="attributes">
                

                

                
                    &lt;repeatable><br>
                
                </td>
            

            

            <td class="description last"><p>option string(s) or string array</p></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="options_custom.js.html">options/custom.js</a>, <a href="options_custom.js.html#line72">line 72</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>FfmpegCommand</p>
</div>



        

    
        <h5>Examples:</h5>
        
    <pre class="prettyprint"><code>command.outputOptions('option1');</code></pre>

    <pre class="prettyprint"><code>command.outputOptions('option1', 'option2');</code></pre>

    <pre class="prettyprint"><code>command.outputOptions(['option1', 'option2']);</code></pre>

    

    
        <h5>Aliases:</h5>
        

<ul>
	
		<li>addOutputOption</li>
	
		<li>addOutputOptions</li>
	
		<li>addOption</li>
	
		<li>addOptions</li>
	
		<li>withOutputOption</li>
	
		<li>withOutputOptions</li>
	
		<li>withOption</li>
	
		<li>withOptions</li>
	
		<li>outputOption</li>
	
</ul>

    
</dd>

        
            
<dt>
    <h4 class="name" id="withInputOption"><span class="type-signature"></span>withInputOption<span class="signature">(options)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#inputOptions">FfmpegCommand#inputOptions</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="withInputOptions"><span class="type-signature"></span>withInputOptions<span class="signature">(options)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#inputOptions">FfmpegCommand#inputOptions</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="withOption"><span class="type-signature"></span>withOption<span class="signature">(options)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#outputOptions">FfmpegCommand#outputOptions</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="withOptions"><span class="type-signature"></span>withOptions<span class="signature">(options)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#outputOptions">FfmpegCommand#outputOptions</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="withOutputOption"><span class="type-signature"></span>withOutputOption<span class="signature">(options)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#outputOptions">FfmpegCommand#outputOptions</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="withOutputOptions"><span class="type-signature"></span>withOutputOptions<span class="signature">(options)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#outputOptions">FfmpegCommand#outputOptions</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        </dl>
    
        <h3 class="subsection-title"><a name="input-methods"></a>Input methods</h3>

        <dl>
            
<dt>
    <h4 class="name" id="addInput"><span class="type-signature"></span>addInput<span class="signature">(source)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#input">FfmpegCommand#input</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="fpsInput"><span class="type-signature"></span>fpsInput<span class="signature">(fps)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#inputFps">FfmpegCommand#inputFps</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="fromFormat"><span class="type-signature"></span>fromFormat<span class="signature">(format)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#inputFormat">FfmpegCommand#inputFormat</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="input"><span class="type-signature"></span>input<span class="signature">(source)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Add an input to command</p>
<p>Also switches &quot;current input&quot;, that is the input that will be affected
by subsequent input-related methods.</p>
<p>Note: only one stream input is supported for now.</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>source</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">Readable</span>


            
            </td>

            

            

            <td class="description last"><p>input file path or readable stream</p></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="options_inputs.js.html">options/inputs.js</a>, <a href="options_inputs.js.html#line11">line 11</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>FfmpegCommand</p>
</div>



        

    

    
        <h5>Aliases:</h5>
        

<ul>
	
		<li>mergeAdd</li>
	
		<li>addInput</li>
	
</ul>

    
</dd>

        
            
<dt>
    <h4 class="name" id="inputFormat"><span class="type-signature"></span>inputFormat<span class="signature">(format)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Specify input format for the last specified input</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>format</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            

            

            <td class="description last"><p>input format</p></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="options_inputs.js.html">options/inputs.js</a>, <a href="options_inputs.js.html#line63">line 63</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>FfmpegCommand</p>
</div>



        

    

    
        <h5>Aliases:</h5>
        

<ul>
	
		<li>withInputFormat</li>
	
		<li>fromFormat</li>
	
</ul>

    
</dd>

        
            
<dt>
    <h4 class="name" id="inputFps"><span class="type-signature"></span>inputFps<span class="signature">(fps)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Specify input FPS for the last specified input
(only valid for raw video formats)</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>fps</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last"><p>input FPS</p></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="options_inputs.js.html">options/inputs.js</a>, <a href="options_inputs.js.html#line85">line 85</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>FfmpegCommand</p>
</div>



        

    

    
        <h5>Aliases:</h5>
        

<ul>
	
		<li>withInputFps</li>
	
		<li>withInputFPS</li>
	
		<li>withFpsInput</li>
	
		<li>withFPSInput</li>
	
		<li>inputFPS</li>
	
		<li>inputFps</li>
	
		<li>fpsInput</li>
	
</ul>

    
</dd>

        
            
<dt>
    <h4 class="name" id="inputFPS"><span class="type-signature"></span>inputFPS<span class="signature">(fps)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#inputFps">FfmpegCommand#inputFps</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="inputFps"><span class="type-signature"></span>inputFps<span class="signature">(fps)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#inputFps">FfmpegCommand#inputFps</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="loop"><span class="type-signature"></span>loop<span class="signature">(<span class="optional">duration</span>)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Loop over the last specified input</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>duration</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">Number</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>loop duration in seconds or as a '[[hh:]mm:]ss[.xxx]' string</p></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="options_inputs.js.html">options/inputs.js</a>, <a href="options_inputs.js.html#line156">line 156</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>FfmpegCommand</p>
</div>



        

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="mergeAdd"><span class="type-signature"></span>mergeAdd<span class="signature">(source)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#input">FfmpegCommand#input</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="native"><span class="type-signature"></span>native<span class="signature">()</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Use native framerate for the last specified input</p>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="options_inputs.js.html">options/inputs.js</a>, <a href="options_inputs.js.html#line113">line 113</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>FfmmegCommand</p>
</div>



        

    

    
        <h5>Aliases:</h5>
        

<ul>
	
		<li>nativeFramerate</li>
	
		<li>withNativeFramerate</li>
	
</ul>

    
</dd>

        
            
<dt>
    <h4 class="name" id="nativeFramerate"><span class="type-signature"></span>nativeFramerate<span class="signature">()</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#native">FfmpegCommand#native</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="seek"><span class="type-signature"></span>seek<span class="signature">(seek)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Specify output seek time</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>seek</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last"><p>seek time in seconds or as a '[hh:[mm:]]ss[.xxx]' string</p></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="options_output.js.html">options/output.js</a>, <a href="options_output.js.html#line79">line 79</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>FfmpegCommand</p>
</div>



        

    

    
        <h5>Alias:</h5>
        

	seekOutput

    
</dd>

        
            
<dt>
    <h4 class="name" id="seekInput"><span class="type-signature"></span>seekInput<span class="signature">(seek)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Specify input seek time for the last specified input</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>seek</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last"><p>seek time in seconds or as a '[hh:[mm:]]ss[.xxx]' string</p></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="options_inputs.js.html">options/inputs.js</a>, <a href="options_inputs.js.html#line134">line 134</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>FfmpegCommand</p>
</div>



        

    

    
        <h5>Aliases:</h5>
        

<ul>
	
		<li>setStartTime</li>
	
		<li>seekTo</li>
	
</ul>

    
</dd>

        
            
<dt>
    <h4 class="name" id="seekOutput"><span class="type-signature"></span>seekOutput<span class="signature">(seek)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#seek">FfmpegCommand#seek</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="seekTo"><span class="type-signature"></span>seekTo<span class="signature">(seek)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#seekInput">FfmpegCommand#seekInput</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="setStartTime"><span class="type-signature"></span>setStartTime<span class="signature">(seek)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#seekInput">FfmpegCommand#seekInput</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="withFpsInput"><span class="type-signature"></span>withFpsInput<span class="signature">(fps)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#inputFps">FfmpegCommand#inputFps</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="withFPSInput"><span class="type-signature"></span>withFPSInput<span class="signature">(fps)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#inputFps">FfmpegCommand#inputFps</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="withInputFormat"><span class="type-signature"></span>withInputFormat<span class="signature">(format)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#inputFormat">FfmpegCommand#inputFormat</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="withInputFPS"><span class="type-signature"></span>withInputFPS<span class="signature">(fps)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#inputFps">FfmpegCommand#inputFps</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="withInputFps"><span class="type-signature"></span>withInputFps<span class="signature">(fps)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#inputFps">FfmpegCommand#inputFps</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="withNativeFramerate"><span class="type-signature"></span>withNativeFramerate<span class="signature">()</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#native">FfmpegCommand#native</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        </dl>
    
        <h3 class="subsection-title"><a name="metadata-methods"></a>Metadata methods</h3>

        <dl>
            
<dt>
    <h4 class="name" id="ffprobe"><span class="type-signature"></span>ffprobe<span class="signature">(<span class="optional">index</span>, <span class="optional">options</span>, callback)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Run ffprobe on last specified input</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>index</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                    &lt;nullable><br>
                

                
                </td>
            

            

            <td class="description last"><p>0-based index of input to probe (defaults to last input)</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>options</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;String></span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                    &lt;nullable><br>
                

                
                </td>
            

            

            <td class="description last"><p>array of output options to return</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>callback</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="FfmpegCommand.html#~ffprobeCallback">FfmpegCommand~ffprobeCallback</a></span>


            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>callback function</p></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="ffprobe.js.html">ffprobe.js</a>, <a href="ffprobe.js.html#line85">line 85</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        </dl>
    
        <h3 class="subsection-title"><a name="miscellaneous-methods"></a>Miscellaneous methods</h3>

        <dl>
            
<dt>
    <h4 class="name" id="preset"><span class="type-signature"></span>preset<span class="signature">(preset)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Use preset</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>preset</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">function</span>


            
            </td>

            

            

            <td class="description last"><p>preset name or preset function</p></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="options_misc.js.html">options/misc.js</a>, <a href="options_misc.js.html#line11">line 11</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    

    

    
        <h5>Alias:</h5>
        

	usingPreset

    
</dd>

        
            
<dt>
    <h4 class="name" id="usingPreset"><span class="type-signature"></span>usingPreset<span class="signature">(preset)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#preset">FfmpegCommand#preset</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        </dl>
    
        <h3 class="subsection-title"><a name="other-methods"></a>Other methods</h3>

        <dl>
            
<dt>
    <h4 class="name" id="clone"><span class="type-signature"></span>clone<span class="signature">()</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Clone an ffmpeg command</p>
<p>This method is useful when you want to process the same input multiple times.
It returns a new FfmpegCommand instance with the exact same options.</p>
<p>All options set <em>after</em> the clone() call will only be applied to the instance
it has been called on.</p>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="fluent-ffmpeg.js.html">fluent-ffmpeg.js</a>, <a href="fluent-ffmpeg.js.html#line84">line 84</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>FfmpegCommand</p>
</div>



        

    
        <h5>Example:</h5>
        
    <pre class="prettyprint"><code>var command = ffmpeg('/path/to/source.avi')
    .audioCodec('libfaac')
    .videoCodec('libx264')
    .format('mp4');

  command.clone()
    .size('320x200')
    .save('/path/to/output-small.mp4');

  command.clone()
    .size('640x400')
    .save('/path/to/output-medium.mp4');

  command.save('/path/to/output-original-size.mp4');</code></pre>

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="setFfmpegPath"><span class="type-signature"></span>setFfmpegPath<span class="signature">(ffmpegPath)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Manually define the ffmpeg binary full path.</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>ffmpegPath</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            

            

            <td class="description last"><p>The full path to the ffmpeg binary.</p></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="capabilities.js.html">capabilities.js</a>, <a href="capabilities.js.html#line25">line 25</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>FfmpegCommand</p>
</div>



        

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="setFfprobePath"><span class="type-signature"></span>setFfprobePath<span class="signature">(ffprobePath)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Manually define the ffprobe binary full path.</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>ffprobePath</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            

            

            <td class="description last"><p>The full path to the ffprobe binary.</p></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="capabilities.js.html">capabilities.js</a>, <a href="capabilities.js.html#line38">line 38</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>FfmpegCommand</p>
</div>



        

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="setFlvtoolPath"><span class="type-signature"></span>setFlvtoolPath<span class="signature">(flvtool)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Manually define the flvtool2/flvmeta binary full path.</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>flvtool</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            

            

            <td class="description last"><p>The full path to the flvtool2 or flvmeta binary.</p></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="capabilities.js.html">capabilities.js</a>, <a href="capabilities.js.html#line51">line 51</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>FfmpegCommand</p>
</div>



        

    

    
</dd>

        </dl>
    
        <h3 class="subsection-title"><a name="output-methods"></a>Output methods</h3>

        <dl>
            
<dt>
    <h4 class="name" id="addOutput"><span class="type-signature"></span>addOutput<span class="signature">(target, <span class="optional">pipeopts</span>)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#output">FfmpegCommand#output</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="duration"><span class="type-signature"></span>duration<span class="signature">(duration)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Set output duration</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>duration</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last"><p>duration in seconds or as a '[[hh:]mm:]ss[.xxx]' string</p></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="options_output.js.html">options/output.js</a>, <a href="options_output.js.html#line96">line 96</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>FfmpegCommand</p>
</div>



        

    

    
        <h5>Aliases:</h5>
        

<ul>
	
		<li>withDuration</li>
	
		<li>setDuration</li>
	
</ul>

    
</dd>

        
            
<dt>
    <h4 class="name" id="flvmeta"><span class="type-signature"></span>flvmeta<span class="signature">()</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Run flvtool2/flvmeta on output</p>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="options_output.js.html">options/output.js</a>, <a href="options_output.js.html#line148">line 148</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>FfmpegCommand</p>
</div>



        

    

    
        <h5>Alias:</h5>
        

	updateFlvMetadata

    
</dd>

        
            
<dt>
    <h4 class="name" id="format"><span class="type-signature"></span>format<span class="signature">(format)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Set output format</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>format</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            

            

            <td class="description last"><p>output format name</p></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="options_output.js.html">options/output.js</a>, <a href="options_output.js.html#line114">line 114</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>FfmpegCommand</p>
</div>



        

    

    
        <h5>Aliases:</h5>
        

<ul>
	
		<li>toFormat</li>
	
		<li>withOutputFormat</li>
	
		<li>outputFormat</li>
	
</ul>

    
</dd>

        
            
<dt>
    <h4 class="name" id="map"><span class="type-signature"></span>map<span class="signature">(spec)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Add stream mapping to output</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>spec</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            

            

            <td class="description last"><p>stream specification string, with optional square brackets</p></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="options_output.js.html">options/output.js</a>, <a href="options_output.js.html#line133">line 133</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>FfmpegCommand</p>
</div>



        

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="output"><span class="type-signature"></span>output<span class="signature">(target, <span class="optional">pipeopts</span>)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Add output</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        
        <th>Default</th>
        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>target</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">Writable</span>


            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            
                <td class="default">
                
                </td>
            

            <td class="description last"><p>target file path or writable stream</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>pipeopts</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                    {}
                
                </td>
            

            <td class="description last"><p>pipe options (only applies to streams)</p></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="options_output.js.html">options/output.js</a>, <a href="options_output.js.html#line12">line 12</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>FfmpegCommand</p>
</div>



        

    

    
        <h5>Alias:</h5>
        

	addOutput

    
</dd>

        
            
<dt>
    <h4 class="name" id="outputFormat"><span class="type-signature"></span>outputFormat<span class="signature">(format)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#format">FfmpegCommand#format</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="setDuration"><span class="type-signature"></span>setDuration<span class="signature">(duration)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#duration">FfmpegCommand#duration</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="toFormat"><span class="type-signature"></span>toFormat<span class="signature">(format)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#format">FfmpegCommand#format</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="updateFlvMetadata"><span class="type-signature"></span>updateFlvMetadata<span class="signature">()</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#flvmeta">FfmpegCommand#flvmeta</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="withDuration"><span class="type-signature"></span>withDuration<span class="signature">(duration)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#duration">FfmpegCommand#duration</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="withOutputFormat"><span class="type-signature"></span>withOutputFormat<span class="signature">(format)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#format">FfmpegCommand#format</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        </dl>
    
        <h3 class="subsection-title"><a name="processing-methods"></a>Processing methods</h3>

        <dl>
            
<dt>
    <h4 class="name" id="concat"><span class="type-signature"></span>concat<span class="signature">(target, <span class="optional">options</span>)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Merge (concatenate) inputs to a single file</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>target</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">Writable</span>


            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>output file or writable stream</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>options</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>pipe options (only used when outputting to a writable stream)</p></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="recipes.js.html">recipes.js</a>, <a href="recipes.js.html#line408">line 408</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>FfmpegCommand</p>
</div>



        

    

    
        <h5>Aliases:</h5>
        

<ul>
	
		<li>concatenate</li>
	
		<li>mergeToFile</li>
	
</ul>

    
</dd>

        
            
<dt>
    <h4 class="name" id="concatenate"><span class="type-signature"></span>concatenate<span class="signature">(target, <span class="optional">options</span>)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#concat">FfmpegCommand#concat</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="exec"><span class="type-signature"></span>exec<span class="signature">()</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#run">FfmpegCommand#run</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="execute"><span class="type-signature"></span>execute<span class="signature">()</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#run">FfmpegCommand#run</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="kill"><span class="type-signature"></span>kill<span class="signature">(<span class="optional">signal</span>)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Kill current ffmpeg process, if any</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        
        <th>Default</th>
        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>signal</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                    SIGKILL
                
                </td>
            

            <td class="description last"><p>signal name</p></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="processor.js.html">processor.js</a>, <a href="processor.js.html#line640">line 640</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>FfmpegCommand</p>
</div>



        

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="mergeToFile"><span class="type-signature"></span>mergeToFile<span class="signature">(target, <span class="optional">options</span>)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#concat">FfmpegCommand#concat</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="pipe"><span class="type-signature"></span>pipe<span class="signature">(<span class="optional">stream</span>, <span class="optional">options</span>)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Execute ffmpeg command and save output to a stream</p>
<p>If 'stream' is not specified, a PassThrough stream is created and returned.
'options' will be used when piping ffmpeg output to the output stream
(@see http://nodejs.org/api/stream.html#stream_readable_pipe_destination_options)</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        
        <th>Default</th>
        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>stream</code></td>
            

            <td class="type">
            
                
<span class="param-type">stream.Writable</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                </td>
            

            <td class="description last"><p>output stream</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>options</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                    {}
                
                </td>
            

            <td class="description last"><p>pipe options</p></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="recipes.js.html">recipes.js</a>, <a href="recipes.js.html#line33">line 33</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>Output stream</p>
</div>



        

    

    
        <h5>Aliases:</h5>
        

<ul>
	
		<li>stream</li>
	
		<li>writeToStream</li>
	
</ul>

    
</dd>

        
            
<dt>
    <h4 class="name" id="renice"><span class="type-signature"></span>renice<span class="signature">(<span class="optional">niceness</span>)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Renice current and/or future ffmpeg processes</p>
<p>Ignored on Windows platforms.</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        
        <th>Default</th>
        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>niceness</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                    0
                
                </td>
            

            <td class="description last"><p>niceness value between -20 (highest priority) and 20 (lowest priority)</p></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="processor.js.html">processor.js</a>, <a href="processor.js.html#line593">line 593</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>FfmpegCommand</p>
</div>



        

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="run"><span class="type-signature"></span>run<span class="signature">()</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Run ffmpeg command</p>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="processor.js.html">processor.js</a>, <a href="processor.js.html#line381">line 381</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    

    

    
        <h5>Aliases:</h5>
        

<ul>
	
		<li>exec</li>
	
		<li>execute</li>
	
</ul>

    
</dd>

        
            
<dt>
    <h4 class="name" id="save"><span class="type-signature"></span>save<span class="signature">(output)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Execute ffmpeg command and save output to a file</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>output</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            

            

            <td class="description last"><p>file path</p></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="recipes.js.html">recipes.js</a>, <a href="recipes.js.html#line16">line 16</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>FfmpegCommand</p>
</div>



        

    

    
        <h5>Alias:</h5>
        

	saveToFile

    
</dd>

        
            
<dt>
    <h4 class="name" id="saveToFile"><span class="type-signature"></span>saveToFile<span class="signature">(output)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#save">FfmpegCommand#save</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="screenshot"><span class="type-signature"></span>screenshot<span class="signature">(<span class="optional">config</span>, <span class="optional">folder</span>)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#screenshots">FfmpegCommand#screenshots</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="screenshots"><span class="type-signature"></span>screenshots<span class="signature">(<span class="optional">config</span>, <span class="optional">folder</span>)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Generate images from a video</p>
<p>Note: this method makes the command emit a 'filenames' event with an array of
the generated image filenames.</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        
        <th>Default</th>
        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>config</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>
|

<span class="param-type">Object</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                    1
                
                </td>
            

            <td class="description last"><p>screenshot count or configuration object with
  the following keys:</p>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        
        <th>Default</th>
        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>count</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                </td>
            

            <td class="description last"><p>number of screenshots to take; using this option
  takes screenshots at regular intervals (eg. count=4 would take screens at 20%, 40%,
  60% and 80% of the video length).</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>folder</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                    '.'
                
                </td>
            

            <td class="description last"><p>output folder</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>filename</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                    'tn.png'
                
                </td>
            

            <td class="description last"><p>output filename pattern, may contain the following
  tokens:</p>
<ul>
<li>'%s': offset in seconds</li>
<li>'%w': screenshot width</li>
<li>'%h': screenshot height</li>
<li>'%r': screenshot resolution (same as '%wx%h')</li>
<li>'%f': input filename</li>
<li>'%b': input basename (filename w/o extension)</li>
<li>'%i': index of screenshot in timemark array (can be zero-padded by using it like <code>%000i</code>)</li>
</ul></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>timemarks</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;Number></span>
|

<span class="param-type">Array.&lt;String></span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                </td>
            

            <td class="description last"><p>array of timemarks to take screenshots
  at; each timemark may be a number of seconds, a '[[hh:]mm:]ss[.xxx]' string or a
  'XX%' string.  Overrides 'count' if present.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>timestamps</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;Number></span>
|

<span class="param-type">Array.&lt;String></span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                </td>
            

            <td class="description last"><p>alias for 'timemarks'</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>fastSeek</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                </td>
            

            <td class="description last"><p>use fast seek (less accurate)</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>size</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                </td>
            

            <td class="description last"><p>screenshot size, with the same syntax as <a href="FfmpegCommand.html#size">FfmpegCommand#size</a></p></td>
        </tr>

    
    </tbody>
</table>
            </td>
        </tr>

    

        <tr>
            
                <td class="name"><code>folder</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                </td>
            

            <td class="description last"><p>output folder (legacy alias for 'config.folder')</p></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="recipes.js.html">recipes.js</a>, <a href="recipes.js.html#line69">line 69</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>FfmpegCommand</p>
</div>



        

    

    
        <h5>Aliases:</h5>
        

<ul>
	
		<li>takeScreenshots</li>
	
		<li>thumbnail</li>
	
		<li>thumbnails</li>
	
		<li>screenshot</li>
	
</ul>

    
</dd>

        
            
<dt>
    <h4 class="name" id="stream"><span class="type-signature"></span>stream<span class="signature">(<span class="optional">stream</span>, <span class="optional">options</span>)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#pipe">FfmpegCommand#pipe</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="takeScreenshots"><span class="type-signature"></span>takeScreenshots<span class="signature">(<span class="optional">config</span>, <span class="optional">folder</span>)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#screenshots">FfmpegCommand#screenshots</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="thumbnail"><span class="type-signature"></span>thumbnail<span class="signature">(<span class="optional">config</span>, <span class="optional">folder</span>)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#screenshots">FfmpegCommand#screenshots</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="thumbnails"><span class="type-signature"></span>thumbnails<span class="signature">(<span class="optional">config</span>, <span class="optional">folder</span>)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#screenshots">FfmpegCommand#screenshots</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="writeToStream"><span class="type-signature"></span>writeToStream<span class="signature">(<span class="optional">stream</span>, <span class="optional">options</span>)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#pipe">FfmpegCommand#pipe</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        </dl>
    
        <h3 class="subsection-title"><a name="video-methods"></a>Video methods</h3>

        <dl>
            
<dt>
    <h4 class="name" id="fps"><span class="type-signature"></span>fps<span class="signature">(fps)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Specify output FPS</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>fps</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last"><p>output FPS</p></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="options_video.js.html">options/video.js</a>, <a href="options_video.js.html#line141">line 141</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>FfmpegCommand</p>
</div>



        

    

    
        <h5>Aliases:</h5>
        

<ul>
	
		<li>withOutputFps</li>
	
		<li>withOutputFPS</li>
	
		<li>withFpsOutput</li>
	
		<li>withFPSOutput</li>
	
		<li>withFps</li>
	
		<li>withFPS</li>
	
		<li>outputFPS</li>
	
		<li>outputFps</li>
	
		<li>fpsOutput</li>
	
		<li>FPSOutput</li>
	
		<li>FPS</li>
	
</ul>

    
</dd>

        
            
<dt>
    <h4 class="name" id="FPS"><span class="type-signature"></span>FPS<span class="signature">(fps)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#fps">FfmpegCommand#fps</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="fpsOutput"><span class="type-signature"></span>fpsOutput<span class="signature">(fps)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#fps">FfmpegCommand#fps</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="FPSOutput"><span class="type-signature"></span>FPSOutput<span class="signature">(fps)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#fps">FfmpegCommand#fps</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="frames"><span class="type-signature"></span>frames<span class="signature">(frames)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Only transcode a certain number of frames</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>frames</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last"><p>frame count</p></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="options_video.js.html">options/video.js</a>, <a href="options_video.js.html#line168">line 168</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>FfmpegCommand</p>
</div>



        

    

    
        <h5>Aliases:</h5>
        

<ul>
	
		<li>takeFrames</li>
	
		<li>withFrames</li>
	
</ul>

    
</dd>

        
            
<dt>
    <h4 class="name" id="noVideo"><span class="type-signature"></span>noVideo<span class="signature">()</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Disable video in the output</p>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="options_video.js.html">options/video.js</a>, <a href="options_video.js.html#line12">line 12</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>FfmpegCommand</p>
</div>



        

    

    
        <h5>Alias:</h5>
        

	withNoVideo

    
</dd>

        
            
<dt>
    <h4 class="name" id="outputFps"><span class="type-signature"></span>outputFps<span class="signature">(fps)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#fps">FfmpegCommand#fps</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="outputFPS"><span class="type-signature"></span>outputFPS<span class="signature">(fps)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#fps">FfmpegCommand#fps</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="takeFrames"><span class="type-signature"></span>takeFrames<span class="signature">(frames)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#frames">FfmpegCommand#frames</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="videoBitrate"><span class="type-signature"></span>videoBitrate<span class="signature">(bitrate, <span class="optional">constant</span>)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Specify video bitrate</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        
        <th>Default</th>
        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>bitrate</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">Number</span>


            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            
                <td class="default">
                
                </td>
            

            <td class="description last"><p>video bitrate in kbps (with an optional 'k' suffix)</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>constant</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                    false
                
                </td>
            

            <td class="description last"><p>enforce constant bitrate</p></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="options_video.js.html">options/video.js</a>, <a href="options_video.js.html#line48">line 48</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>FfmpegCommand</p>
</div>



        

    

    
        <h5>Alias:</h5>
        

	withVideoBitrate

    
</dd>

        
            
<dt>
    <h4 class="name" id="videoCodec"><span class="type-signature"></span>videoCodec<span class="signature">(codec)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Specify video codec</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>codec</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            

            

            <td class="description last"><p>video codec name</p></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="options_video.js.html">options/video.js</a>, <a href="options_video.js.html#line31">line 31</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>FfmpegCommand</p>
</div>



        

    

    
        <h5>Alias:</h5>
        

	withVideoCodec

    
</dd>

        
            
<dt>
    <h4 class="name" id="videoFilter"><span class="type-signature"></span>videoFilter<span class="signature">(filters)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#videoFilters">FfmpegCommand#videoFilters</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="videoFilters"><span class="type-signature"></span>videoFilters<span class="signature">(filters)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Specify custom video filter(s)</p>
<p>Can be called both with one or many filters, or a filter array.</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>filters</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">Array.&lt;String></span>
|

<span class="param-type">Array.&lt;Object></span>


            
            </td>

            

            

            <td class="description last"><p>video filter strings, string array or
  filter specification array, each with the following properties:</p>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>filter</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>filter name</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>options</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">Array.&lt;String></span>
|

<span class="param-type">Object</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>filter option string, array, or object</p></td>
        </tr>

    
    </tbody>
</table>
            </td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="options_video.js.html">options/video.js</a>, <a href="options_video.js.html#line76">line 76</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>FfmpegCommand</p>
</div>



        

    
        <h5>Examples:</h5>
        
    <pre class="prettyprint"><code>command.videoFilters('filter1');</code></pre>

    <pre class="prettyprint"><code>command.videoFilters('filter1', 'filter2=param1=value1:param2=value2');</code></pre>

    <pre class="prettyprint"><code>command.videoFilters(['filter1', 'filter2']);</code></pre>

    <pre class="prettyprint"><code>command.videoFilters([
  {
    filter: 'filter1'
  },
  {
    filter: 'filter2',
    options: 'param=value:param=value'
  }
]);</code></pre>

    <pre class="prettyprint"><code>command.videoFilters(
  {
    filter: 'filter1',
    options: ['value1', 'value2']
  },
  {
    filter: 'filter2',
    options: { param1: 'value1', param2: 'value2' }
  }
);</code></pre>

    

    
        <h5>Aliases:</h5>
        

<ul>
	
		<li>withVideoFilter</li>
	
		<li>withVideoFilters</li>
	
		<li>videoFilter</li>
	
</ul>

    
</dd>

        
            
<dt>
    <h4 class="name" id="withFPS"><span class="type-signature"></span>withFPS<span class="signature">(fps)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#fps">FfmpegCommand#fps</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="withFps"><span class="type-signature"></span>withFps<span class="signature">(fps)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#fps">FfmpegCommand#fps</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="withFPSOutput"><span class="type-signature"></span>withFPSOutput<span class="signature">(fps)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#fps">FfmpegCommand#fps</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="withFpsOutput"><span class="type-signature"></span>withFpsOutput<span class="signature">(fps)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#fps">FfmpegCommand#fps</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="withFrames"><span class="type-signature"></span>withFrames<span class="signature">(frames)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#frames">FfmpegCommand#frames</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="withNoVideo"><span class="type-signature"></span>withNoVideo<span class="signature">()</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#noVideo">FfmpegCommand#noVideo</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="withOutputFps"><span class="type-signature"></span>withOutputFps<span class="signature">(fps)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#fps">FfmpegCommand#fps</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="withOutputFPS"><span class="type-signature"></span>withOutputFPS<span class="signature">(fps)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#fps">FfmpegCommand#fps</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="withVideoBitrate"><span class="type-signature"></span>withVideoBitrate<span class="signature">(bitrate, <span class="optional">constant</span>)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#videoBitrate">FfmpegCommand#videoBitrate</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="withVideoCodec"><span class="type-signature"></span>withVideoCodec<span class="signature">(codec)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#videoCodec">FfmpegCommand#videoCodec</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="withVideoFilter"><span class="type-signature"></span>withVideoFilter<span class="signature">(filters)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#videoFilters">FfmpegCommand#videoFilters</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="withVideoFilters"><span class="type-signature"></span>withVideoFilters<span class="signature">(filters)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#videoFilters">FfmpegCommand#videoFilters</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        </dl>
    
        <h3 class="subsection-title"><a name="video-size-methods"></a>Video size methods</h3>

        <dl>
            
<dt>
    <h4 class="name" id="applyAutoPad"><span class="type-signature"></span>applyAutoPad<span class="signature">(<span class="optional">pad</span>, <span class="optional">color</span>)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#autopad">FfmpegCommand#autopad</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="applyAutopad"><span class="type-signature"></span>applyAutopad<span class="signature">(<span class="optional">pad</span>, <span class="optional">color</span>)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#autopad">FfmpegCommand#autopad</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="applyAutoPadding"><span class="type-signature"></span>applyAutoPadding<span class="signature">(<span class="optional">pad</span>, <span class="optional">color</span>)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#autopad">FfmpegCommand#autopad</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="applyAutopadding"><span class="type-signature"></span>applyAutopadding<span class="signature">(<span class="optional">pad</span>, <span class="optional">color</span>)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#autopad">FfmpegCommand#autopad</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="aspect"><span class="type-signature"></span>aspect<span class="signature">(aspect)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Set output aspect ratio</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>aspect</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last"><p>aspect ratio (number or 'X:Y' string)</p></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="options_videosize.js.html">options/videosize.js</a>, <a href="options_videosize.js.html#line218">line 218</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>FfmpegCommand</p>
</div>



        

    

    
        <h5>Aliases:</h5>
        

<ul>
	
		<li>withAspect</li>
	
		<li>withAspectRatio</li>
	
		<li>setAspect</li>
	
		<li>setAspectRatio</li>
	
		<li>aspectRatio</li>
	
</ul>

    
</dd>

        
            
<dt>
    <h4 class="name" id="aspectRatio"><span class="type-signature"></span>aspectRatio<span class="signature">(aspect)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#aspect">FfmpegCommand#aspect</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="autopad"><span class="type-signature"></span>autopad<span class="signature">(<span class="optional">pad</span>, <span class="optional">color</span>)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Enable auto-padding the output</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        
        <th>Default</th>
        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>pad</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                    true
                
                </td>
            

            <td class="description last"><p>enable/disable auto-padding</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>color</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                    'black'
                
                </td>
            

            <td class="description last"><p>pad color</p></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="options_videosize.js.html">options/videosize.js</a>, <a href="options_videosize.js.html#line253">line 253</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    

    

    
        <h5>Aliases:</h5>
        

<ul>
	
		<li>applyAutopadding</li>
	
		<li>applyAutoPadding</li>
	
		<li>applyAutopad</li>
	
		<li>applyAutoPad</li>
	
		<li>withAutopadding</li>
	
		<li>withAutoPadding</li>
	
		<li>withAutopad</li>
	
		<li>withAutoPad</li>
	
		<li>autoPad</li>
	
</ul>

    
</dd>

        
            
<dt>
    <h4 class="name" id="autoPad"><span class="type-signature"></span>autoPad<span class="signature">(<span class="optional">pad</span>, <span class="optional">color</span>)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#autopad">FfmpegCommand#autopad</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="keepDAR"><span class="type-signature"></span>keepDAR<span class="signature">()</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Keep display aspect ratio</p>
<p>This method is useful when converting an input with non-square pixels to an output format
that does not support non-square pixels.  It rescales the input so that the display aspect
ratio is the same.</p>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="options_videosize.js.html">options/videosize.js</a>, <a href="options_videosize.js.html#line155">line 155</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>FfmpegCommand</p>
</div>



        

    

    
        <h5>Aliases:</h5>
        

<ul>
	
		<li>keepPixelAspect</li>
	
		<li>keepDisplayAspect</li>
	
		<li>keepDisplayAspectRatio</li>
	
</ul>

    
</dd>

        
            
<dt>
    <h4 class="name" id="keepDisplayAspect"><span class="type-signature"></span>keepDisplayAspect<span class="signature">()</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#keepDAR">FfmpegCommand#keepDAR</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="keepDisplayAspectRatio"><span class="type-signature"></span>keepDisplayAspectRatio<span class="signature">()</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#keepDAR">FfmpegCommand#keepDAR</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="keepPixelAspect"><span class="type-signature"></span>keepPixelAspect<span class="signature">()</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#keepDAR">FfmpegCommand#keepDAR</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="setAspect"><span class="type-signature"></span>setAspect<span class="signature">(aspect)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#aspect">FfmpegCommand#aspect</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="setAspectRatio"><span class="type-signature"></span>setAspectRatio<span class="signature">(aspect)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#aspect">FfmpegCommand#aspect</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="setSize"><span class="type-signature"></span>setSize<span class="signature">(size)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#size">FfmpegCommand#size</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="size"><span class="type-signature"></span>size<span class="signature">(size)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Set output size</p>
<p>The 'size' parameter can have one of 4 forms:</p>
<ul>
<li>'X%': rescale to xx % of the original size</li>
<li>'WxH': specify width and height</li>
<li>'Wx?': specify width and compute height from input aspect ratio</li>
<li>'?xH': specify height and compute width from input aspect ratio</li>
</ul>
<p>Note: both dimensions will be truncated to multiples of 2.</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>size</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            

            

            <td class="description last"><p>size string, eg. '33%', '320x240', '320x?', '?x240'</p></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="options_videosize.js.html">options/videosize.js</a>, <a href="options_videosize.js.html#line188">line 188</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>FfmpegCommand</p>
</div>



        

    

    
        <h5>Aliases:</h5>
        

<ul>
	
		<li>withSize</li>
	
		<li>setSize</li>
	
</ul>

    
</dd>

        
            
<dt>
    <h4 class="name" id="withAspect"><span class="type-signature"></span>withAspect<span class="signature">(aspect)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#aspect">FfmpegCommand#aspect</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="withAspectRatio"><span class="type-signature"></span>withAspectRatio<span class="signature">(aspect)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#aspect">FfmpegCommand#aspect</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="withAutoPad"><span class="type-signature"></span>withAutoPad<span class="signature">(<span class="optional">pad</span>, <span class="optional">color</span>)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#autopad">FfmpegCommand#autopad</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="withAutopad"><span class="type-signature"></span>withAutopad<span class="signature">(<span class="optional">pad</span>, <span class="optional">color</span>)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#autopad">FfmpegCommand#autopad</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="withAutopadding"><span class="type-signature"></span>withAutopadding<span class="signature">(<span class="optional">pad</span>, <span class="optional">color</span>)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#autopad">FfmpegCommand#autopad</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="withAutoPadding"><span class="type-signature"></span>withAutoPadding<span class="signature">(<span class="optional">pad</span>, <span class="optional">color</span>)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#autopad">FfmpegCommand#autopad</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="withSize"><span class="type-signature"></span>withSize<span class="signature">(size)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        Alias for <a href="#size">FfmpegCommand#size</a>
    </div>
    

    

    

    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        </dl>
    

    
        <h3 class="subsection-title">Type Definitions</h3>

        <dl>
                
<dt>
    <h4 class="name" id="~codecCallback"><span class="type-signature"></span>codecCallback<span class="signature">(err, codecs)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>A callback passed to <a href="FfmpegCommand.html#availableCodecs">FfmpegCommand#availableCodecs</a>.</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>err</code></td>
            

            <td class="type">
            
                
<span class="param-type">Error</span>
|

<span class="param-type">null</span>


            
            </td>

            

            

            <td class="description last"><p>error object or null if no error happened</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>codecs</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>


            
            </td>

            

            

            <td class="description last"><p>codec object with codec names as keys and the following
  properties for each codec (more properties may be available depending on the
  ffmpeg version used):</p>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>description</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            

            

            <td class="description last"><p>codec description</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>canDecode</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>


            
            </td>

            

            

            <td class="description last"><p>whether the codec is able to decode streams</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>canEncode</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>


            
            </td>

            

            

            <td class="description last"><p>whether the codec is able to encode streams</p></td>
        </tr>

    
    </tbody>
</table>
            </td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="capabilities.js.html">capabilities.js</a>, <a href="capabilities.js.html#line330">line 330</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

            
                
<dt>
    <h4 class="name" id="~encodersCallback"><span class="type-signature"></span>encodersCallback<span class="signature">(err, encoders)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>A callback passed to <a href="FfmpegCommand.html#availableEncoders">FfmpegCommand#availableEncoders</a>.</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>err</code></td>
            

            <td class="type">
            
                
<span class="param-type">Error</span>
|

<span class="param-type">null</span>


            
            </td>

            

            

            <td class="description last"><p>error object or null if no error happened</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>encoders</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>


            
            </td>

            

            

            <td class="description last"><p>encoders object with encoder names as keys and the following
  properties for each encoder:</p>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>description</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            

            

            <td class="description last"><p>codec description</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>type</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>


            
            </td>

            

            

            <td class="description last"><p>&quot;audio&quot;, &quot;video&quot; or &quot;subtitle&quot;</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>frameMT</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>


            
            </td>

            

            

            <td class="description last"><p>whether the encoder is able to do frame-level multithreading</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>sliceMT</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>


            
            </td>

            

            

            <td class="description last"><p>whether the encoder is able to do slice-level multithreading</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>experimental</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>


            
            </td>

            

            

            <td class="description last"><p>whether the encoder is experimental</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>drawHorizBand</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>


            
            </td>

            

            

            <td class="description last"><p>whether the encoder supports draw_horiz_band</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>directRendering</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>


            
            </td>

            

            

            <td class="description last"><p>whether the encoder supports direct encoding method 1</p></td>
        </tr>

    
    </tbody>
</table>
            </td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="capabilities.js.html">capabilities.js</a>, <a href="capabilities.js.html#line429">line 429</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

            
                
<dt>
    <h4 class="name" id="~ffprobeCallback"><span class="type-signature"></span>ffprobeCallback<span class="signature">(err, ffprobeData)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>A callback passed to the <a href="FfmpegCommand.html#ffprobe">FfmpegCommand#ffprobe</a> method.</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>err</code></td>
            

            <td class="type">
            
                
<span class="param-type">Error</span>
|

<span class="param-type">null</span>


            
            </td>

            

            

            <td class="description last"><p>error object or null if no error happened</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>ffprobeData</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>


            
            </td>

            

            

            <td class="description last"><p>ffprobe output data; this object
  has the same format as what the following command returns:</p>
<pre class="prettyprint source"><code>`ffprobe -print_format json -show_streams -show_format INPUTFILE`</code></pre>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>streams</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array</span>


            
            </td>

            

            

            <td class="description last"><p>stream information</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>format</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>


            
            </td>

            

            

            <td class="description last"><p>format information</p></td>
        </tr>

    
    </tbody>
</table>
            </td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="ffprobe.js.html">ffprobe.js</a>, <a href="ffprobe.js.html#line71">line 71</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

            
                
<dt>
    <h4 class="name" id="~filterCallback"><span class="type-signature"></span>filterCallback<span class="signature">(err, filters)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>A callback passed to <a href="FfmpegCommand.html#availableFilters">FfmpegCommand#availableFilters</a>.</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>err</code></td>
            

            <td class="type">
            
                
<span class="param-type">Error</span>
|

<span class="param-type">null</span>


            
            </td>

            

            

            <td class="description last"><p>error object or null if no error happened</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>filters</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>


            
            </td>

            

            

            <td class="description last"><p>filter object with filter names as keys and the following
  properties for each filter:</p>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>description</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            

            

            <td class="description last"><p>filter description</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>input</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            

            

            <td class="description last"><p>input type, one of 'audio', 'video' and 'none'</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>multipleInputs</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>


            
            </td>

            

            

            <td class="description last"><p>whether the filter supports multiple inputs</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>output</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            

            

            <td class="description last"><p>output type, one of 'audio', 'video' and 'none'</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>multipleOutputs</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>


            
            </td>

            

            

            <td class="description last"><p>whether the filter supports multiple outputs</p></td>
        </tr>

    
    </tbody>
</table>
            </td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="capabilities.js.html">capabilities.js</a>, <a href="capabilities.js.html#line273">line 273</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

            
                
<dt>
    <h4 class="name" id="~formatCallback"><span class="type-signature"></span>formatCallback<span class="signature">(err, formats)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>A callback passed to <a href="FfmpegCommand.html#availableFormats">FfmpegCommand#availableFormats</a>.</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>err</code></td>
            

            <td class="type">
            
                
<span class="param-type">Error</span>
|

<span class="param-type">null</span>


            
            </td>

            

            

            <td class="description last"><p>error object or null if no error happened</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>formats</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>


            
            </td>

            

            

            <td class="description last"><p>format object with format names as keys and the following
  properties for each format:</p>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>description</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            

            

            <td class="description last"><p>format description</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>canDemux</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>


            
            </td>

            

            

            <td class="description last"><p>whether the format is able to demux streams from an input file</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>canMux</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>


            
            </td>

            

            

            <td class="description last"><p>whether the format is able to mux streams into an output file</p></td>
        </tr>

    
    </tbody>
</table>
            </td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="capabilities.js.html">capabilities.js</a>, <a href="capabilities.js.html#line489">line 489</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

            </dl>
    

    
        <h3 class="subsection-title">Events</h3>

        <dl>
            
<dt>
    <h4 class="name" id="event:codecData">codecData</h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Emitted when ffmpeg reports input codec data</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>codecData</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>


            
            </td>

            

            

            <td class="description last"><p>codec data object</p>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>format</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            

            

            <td class="description last"><p>input format name</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>audio</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            

            

            <td class="description last"><p>input audio codec name</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>audio_details</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            

            

            <td class="description last"><p>input audio codec parameters</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>video</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            

            

            <td class="description last"><p>input video codec name</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>video_details</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            

            

            <td class="description last"><p>input video codec parameters</p></td>
        </tr>

    
    </tbody>
</table>
            </td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="processor.js.html">processor.js</a>, <a href="processor.js.html#line58">line 58</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="event:end">end</h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Emitted when a command finishes processing</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>filenames|stdout</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array</span>
|

<span class="param-type">String</span>
|

<span class="param-type">null</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>generated filenames when taking screenshots, ffmpeg stdout when not outputting to a stream, null otherwise</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>stderr</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">null</span>


            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>ffmpeg stderr</p></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="processor.js.html">processor.js</a>, <a href="processor.js.html#line79">line 79</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="event:error">error</h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Emitted when an error happens when preparing or running a command</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>error</code></td>
            

            <td class="type">
            
                
<span class="param-type">Error</span>


            
            </td>

            

            

            <td class="description last"><p>error object</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>stdout</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">null</span>


            
            </td>

            

            

            <td class="description last"><p>ffmpeg stdout, unless outputting to a stream</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>stderr</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">null</span>


            
            </td>

            

            

            <td class="description last"><p>ffmpeg stderr</p></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="processor.js.html">processor.js</a>, <a href="processor.js.html#line70">line 70</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="event:progress">progress</h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Emitted when ffmpeg reports progress information</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>progress</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>


            
            </td>

            

            

            <td class="description last"><p>progress object</p>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>frames</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>number of frames transcoded</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>currentFps</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>current processing speed in frames per second</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>currentKbps</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>current output generation speed in kilobytes per second</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>targetSize</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>current output file size</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>timemark</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>current video timemark</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>percent</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>processing progress (may not be available depending on input)</p></td>
        </tr>

    
    </tbody>
</table>
            </td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="processor.js.html">processor.js</a>, <a href="processor.js.html#line38">line 38</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="event:start">start</h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Emitted just after ffmpeg has been spawned.</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>command</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            

            

            <td class="description last"><p>ffmpeg command line</p></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="processor.js.html">processor.js</a>, <a href="processor.js.html#line31">line 31</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="event:stderr">stderr</h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Emitted when ffmpeg outputs to stderr</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>line</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            

            

            <td class="description last"><p>stderr output line</p></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="processor.js.html">processor.js</a>, <a href="processor.js.html#line51">line 51</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    

    

    
</dd>

        </dl>
    
</article>

</section>




</div>

<nav>
    <h2><a href="index.html">Index</a></h2><ul><li><a href="index.html#installation">Installation</a></li><ul></ul><li><a href="index.html#usage">Usage</a></li><ul><li><a href="index.html#prerequisites">Prerequisites</a></li><li><a href="index.html#creating-an-ffmpeg-command">Creating an FFmpeg command</a></li><li><a href="index.html#specifying-inputs">Specifying inputs</a></li><li><a href="index.html#input-options">Input options</a></li><li><a href="index.html#audio-options">Audio options</a></li><li><a href="index.html#video-options">Video options</a></li><li><a href="index.html#video-frame-size-options">Video frame size options</a></li><li><a href="index.html#specifying-multiple-outputs">Specifying multiple outputs</a></li><li><a href="index.html#output-options">Output options</a></li><li><a href="index.html#miscellaneous-options">Miscellaneous options</a></li><li><a href="index.html#setting-event-handlers">Setting event handlers</a></li><li><a href="index.html#starting-ffmpeg-processing">Starting FFmpeg processing</a></li><li><a href="index.html#controlling-the-ffmpeg-process">Controlling the FFmpeg process</a></li><li><a href="index.html#reading-video-metadata">Reading video metadata</a></li><li><a href="index.html#querying-ffmpeg-capabilities">Querying ffmpeg capabilities</a></li><li><a href="index.html#cloning-an-ffmpegcommand">Cloning an FfmpegCommand</a></li></ul><li><a href="index.html#contributing">Contributing</a></li><ul><li><a href="index.html#code-contributions">Code contributions</a></li><li><a href="index.html#documentation-contributions">Documentation contributions</a></li><li><a href="index.html#updating-the-documentation">Updating the documentation</a></li><li><a href="index.html#running-tests">Running tests</a></li></ul><li><a href="index.html#main-contributors">Main contributors</a></li><ul></ul><li><a href="index.html#license">License</a></li><ul></ul></ul><h3>Classes</h3><ul><li><a href="FfmpegCommand.html">FfmpegCommand</a></li><ul><li> <a href="FfmpegCommand.html#audio-methods">Audio methods</a></li><li> <a href="FfmpegCommand.html#capabilities-methods">Capabilities methods</a></li><li> <a href="FfmpegCommand.html#custom-options-methods">Custom options methods</a></li><li> <a href="FfmpegCommand.html#input-methods">Input methods</a></li><li> <a href="FfmpegCommand.html#metadata-methods">Metadata methods</a></li><li> <a href="FfmpegCommand.html#miscellaneous-methods">Miscellaneous methods</a></li><li> <a href="FfmpegCommand.html#other-methods">Other methods</a></li><li> <a href="FfmpegCommand.html#output-methods">Output methods</a></li><li> <a href="FfmpegCommand.html#processing-methods">Processing methods</a></li><li> <a href="FfmpegCommand.html#video-methods">Video methods</a></li><li> <a href="FfmpegCommand.html#video-size-methods">Video size methods</a></li></ul></ul>
</nav>

<br clear="both">

<footer>
    Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.4.0</a> on Sun May 01 2016 12:10:37 GMT+0200 (CEST)
</footer>

<script> prettyPrint(); </script>
<script src="scripts/linenumber.js"> </script>
</body>
</html>