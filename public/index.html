<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video Framer</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <h1>Video Framer</h1>
        
        <!-- Progress Bar -->
        <div class="progress-container">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="progress-steps">
                <div class="step active" id="step1">1. Doctor Name</div>
                <div class="step" id="step2">2. Upload & Crop</div>
                <div class="step" id="step3">3. Processing</div>
                <div class="step" id="step4">4. Download</div>
            </div>
        </div>

        <!-- Step 1: Doctor Name -->
        <div class="step-content active" id="stepContent1">
            <div class="form-group">
                <label for="doctorName">Enter Doctor Name:</label>
                <input type="text" id="doctorName" placeholder="Dr. <PERSON>e" required>
                <button onclick="nextStep(1)" class="btn-primary">Next</button>
            </div>
        </div>

        <!-- Step 2: Upload Video -->
        <div class="step-content" id="stepContent2">
            <div class="form-group">
                <label for="videoFile">Upload Video:</label>
                <input type="file" id="videoFile" accept="video/*" required>
                <div class="upload-info">
                    <p>Upload your video and crop it using mouse controls</p>
                </div>
                <button onclick="uploadVideo()" class="btn-primary" id="uploadBtn">Upload & Crop</button>
            </div>
        </div>

        <!-- Step 3: Cropping Interface -->
        <div class="step-content" id="stepContent3">
            <div class="crop-container">
                <h3>Crop Your Video</h3>
                <div class="video-container">
                    <video id="videoPreview" muted></video>
                    <canvas id="cropCanvas"></canvas>
                </div>
                <div class="crop-info">
                    <p>Default 4:4 square crop • Drag to move • Drag edges/corners to resize • Click anywhere to start new crop</p>
                    <div class="crop-values">
                        <span>X: <span id="cropX">0</span></span>
                        <span>Y: <span id="cropY">0</span></span>
                        <span>Width: <span id="cropWidth">200</span></span>
                        <span>Height: <span id="cropHeight">200</span></span>
                    </div>
                </div>
                <button onclick="processCrop()" class="btn-primary" id="cropBtn">Process Video</button>
            </div>
        </div>

        <!-- Step 4: Processing -->
        <div class="step-content" id="stepContent4">
            <div class="processing-container">
                <div class="spinner"></div>
                <h3>Processing Video...</h3>
                <p>Please wait while we create your final video</p>
                <div class="processing-steps">
                    <div class="processing-step" id="processStep1">✓ Video uploaded</div>
                    <div class="processing-step" id="processStep2">⏳ Cropping video...</div>
                    <div class="processing-step" id="processStep3">⏳ Adding overlay...</div>
                    <div class="processing-step" id="processStep4">⏳ Finalizing...</div>
                </div>
            </div>
        </div>

        <!-- Step 5: Download -->
        <div class="step-content" id="stepContent5">
            <div class="download-container">
                <div class="success-icon">✓</div>
                <h3>Video Ready!</h3>
                <p>Your video has been processed successfully</p>
                <div class="final-video-preview">
                    <video id="finalVideo" controls></video>
                </div>
                <button onclick="downloadVideo()" class="btn-download" id="downloadBtn">Download Video</button>
                <button onclick="startOver()" class="btn-secondary">Create Another Video</button>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
