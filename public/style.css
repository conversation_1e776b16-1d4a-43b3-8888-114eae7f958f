* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    overflow: hidden;
}

h1 {
    text-align: center;
    padding: 30px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    margin: 0;
    font-size: 2.5em;
    font-weight: 300;
}

/* Progress Bar */
.progress-container {
    padding: 30px;
    background: #f8f9fa;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 20px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    width: 25%;
    transition: width 0.5s ease;
}

.progress-steps {
    display: flex;
    justify-content: space-between;
}

.step {
    flex: 1;
    text-align: center;
    padding: 10px;
    color: #6c757d;
    font-size: 14px;
    position: relative;
}

.step.active {
    color: #667eea;
    font-weight: 600;
}

.step.completed {
    color: #28a745;
}

/* Step Content */
.step-content {
    display: none;
    padding: 40px;
}

.step-content.active {
    display: block;
}

.form-group {
    margin-bottom: 30px;
}

label {
    display: block;
    margin-bottom: 10px;
    font-weight: 600;
    color: #333;
}

input[type="text"], input[type="file"] {
    width: 100%;
    padding: 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

input[type="text"]:focus, input[type="file"]:focus {
    outline: none;
    border-color: #667eea;
}

/* Buttons */
.btn-primary, .btn-secondary, .btn-download {
    padding: 15px 30px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 10px 5px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-download {
    background: #28a745;
    color: white;
    font-size: 18px;
    padding: 20px 40px;
}

/* Video Container */
.video-container {
    position: relative;
    margin: 20px 0;
    background: #000;
    border-radius: 8px;
    overflow: hidden;
}

#videoPreview {
    width: 100%;
    max-height: 400px;
    display: block;
}

#cropCanvas {
    position: absolute;
    top: 0;
    left: 0;
    cursor: crosshair;
    pointer-events: auto;
}

/* Crop Info */
.crop-info {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin: 20px 0;
}



.crop-values {
    display: flex;
    gap: 15px;
    margin-top: 15px;
    flex-wrap: wrap;
}

.crop-values span {
    background: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-family: monospace;
    border: 1px solid #dee2e6;
    font-size: 14px;
}

/* Processing */
.processing-container {
    text-align: center;
    padding: 40px;
}

.spinner {
    width: 60px;
    height: 60px;
    border: 6px solid #f3f3f3;
    border-top: 6px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 30px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Video crop animations */
.crop-animation {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 255, 0, 0.3);
    border: 2px solid #00ff00;
    border-radius: 10px;
    padding: 20px;
    font-size: 18px;
    font-weight: bold;
    color: white;
    z-index: 1000;
    animation: cropPulse 2s infinite;
}

@keyframes cropPulse {
    0%, 100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.8;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.1);
        opacity: 1;
    }
}

.crop-complete {
    animation: cropComplete 1s ease-out;
}

@keyframes cropComplete {
    0% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.3);
        opacity: 0.8;
    }
    100% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0;
    }
}

.processing-steps {
    text-align: left;
    max-width: 300px;
    margin: 30px auto;
}

.processing-step {
    padding: 10px 0;
    font-size: 16px;
}

/* Download */
.download-container {
    text-align: center;
    padding: 40px;
}

.success-icon {
    width: 80px;
    height: 80px;
    background: #28a745;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 40px;
    margin: 0 auto 30px;
}

.final-video-preview {
    margin: 30px 0;
    background: #000;
    border-radius: 8px;
    overflow: hidden;
}

#finalVideo {
    width: 100%;
    max-height: 400px;
}

.upload-info {
    background: #e3f2fd;
    padding: 15px;
    border-radius: 8px;
    margin: 15px 0;
    border-left: 4px solid #2196f3;
}

.upload-info p {
    margin: 0;
    color: #1976d2;
}

/* Responsive */
@media (max-width: 768px) {
    .container {
        margin: 10px;
        border-radius: 10px;
    }
    
    h1 {
        font-size: 2em;
        padding: 20px;
    }
    
    .step-content {
        padding: 20px;
    }
    
    .progress-steps {
        flex-direction: column;
        gap: 10px;
    }
    
    .crop-values {
        flex-direction: column;
        gap: 10px;
    }
}
