let currentStep = 1;
let uploadedFilename = '';
let videoDimensions = { width: 0, height: 0 };
let cropData = { x: 0, y: 0, width: 200, height: 200 };
let finalVideoFilename = '';

// Cropping variables
let isDragging = false;
let isResizing = false;
let dragStartX, dragStartY;
let resizeHandle = null;
let cropStartData = null;

// Progress management
function updateProgress(step) {
    const progressFill = document.getElementById('progressFill');
    const progressPercentage = (step / 4) * 100;
    progressFill.style.width = progressPercentage + '%';
    
    // Update step indicators
    for (let i = 1; i <= 4; i++) {
        const stepElement = document.getElementById(`step${i}`);
        stepElement.classList.remove('active', 'completed');
        
        if (i < step) {
            stepElement.classList.add('completed');
        } else if (i === step) {
            stepElement.classList.add('active');
        }
    }
}

// Step navigation
function nextStep(fromStep) {
    if (fromStep === 1) {
        const doctorName = document.getElementById('doctorName').value.trim();
        if (!doctorName) {
            alert('Please enter doctor name');
            return;
        }
    }
    
    currentStep = fromStep + 1;
    showStep(currentStep);
    updateProgress(currentStep);
}

function showStep(step) {
    // Hide all step contents
    const stepContents = document.querySelectorAll('.step-content');
    stepContents.forEach(content => content.classList.remove('active'));
    
    // Show current step
    document.getElementById(`stepContent${step}`).classList.add('active');
}

// Video upload
async function uploadVideo() {
    const fileInput = document.getElementById('videoFile');
    const file = fileInput.files[0];
    
    if (!file) {
        alert('Please select a video file');
        return;
    }
    
    const uploadBtn = document.getElementById('uploadBtn');
    uploadBtn.textContent = 'Uploading...';
    uploadBtn.disabled = true;
    
    const formData = new FormData();
    formData.append('video', file);
    
    try {
        const response = await fetch('/digilabs/v2/videoframmer/upload', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            uploadedFilename = result.filename;
            videoDimensions = result.dimensions;
            
            console.log('Video uploaded:', uploadedFilename, 'Dimensions:', videoDimensions);
            
            setupCropInterface();
            nextStep(2);
        } else {
            alert('Upload failed: ' + result.error);
        }
    } catch (error) {
        alert('Upload error: ' + error.message);
    }
    
    uploadBtn.textContent = 'Upload & Crop';
    uploadBtn.disabled = false;
}

// Setup crop interface
function setupCropInterface() {
    const video = document.getElementById('videoPreview');
    const canvas = document.getElementById('cropCanvas');
    
    video.src = `/uploads/${uploadedFilename}`;
    
    video.onloadedmetadata = () => {
        const container = video.parentElement;
        const containerRect = container.getBoundingClientRect();
        
        // Set video size to fit container
        const aspectRatio = videoDimensions.width / videoDimensions.height;
        let displayWidth = Math.min(containerRect.width - 40, 600);
        let displayHeight = displayWidth / aspectRatio;
        
        if (displayHeight > 400) {
            displayHeight = 400;
            displayWidth = displayHeight * aspectRatio;
        }
        
        video.style.width = displayWidth + 'px';
        video.style.height = displayHeight + 'px';
        
        // Set canvas to match video display size
        canvas.width = displayWidth;
        canvas.height = displayHeight;
        canvas.style.width = displayWidth + 'px';
        canvas.style.height = displayHeight + 'px';
        
        // Initialize crop area (center of video)
        cropData = {
            x: Math.floor(videoDimensions.width * 0.25),
            y: Math.floor(videoDimensions.height * 0.25),
            width: Math.floor(videoDimensions.width * 0.5),
            height: Math.floor(videoDimensions.height * 0.5)
        };
        
        setupCanvasEvents();
        drawCropArea();
        updateCropValues();
    };
}

// Setup canvas mouse events
function setupCanvasEvents() {
    const canvas = document.getElementById('cropCanvas');

    canvas.addEventListener('mousedown', onMouseDown);
    canvas.addEventListener('mousemove', onMouseMove);
    canvas.addEventListener('mouseup', onMouseUp);
    canvas.addEventListener('mouseleave', onMouseUp);

    // Change cursor based on position
    canvas.addEventListener('mousemove', updateCursor);
}

function updateCursor(e) {
    if (isDragging || isResizing) return;

    const canvas = e.target;
    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    const handle = getResizeHandle(x, y, canvas);

    if (handle) {
        canvas.style.cursor = getResizeCursor(handle);
    } else if (isInsideCropArea(x, y, canvas)) {
        canvas.style.cursor = 'move';
    } else {
        canvas.style.cursor = 'crosshair';
    }
}

function getResizeCursor(handle) {
    const cursors = {
        'nw': 'nw-resize', 'n': 'n-resize', 'ne': 'ne-resize',
        'e': 'e-resize', 'se': 'se-resize', 's': 's-resize',
        'sw': 'sw-resize', 'w': 'w-resize'
    };
    return cursors[handle] || 'default';
}

function onMouseDown(e) {
    const canvas = e.target;
    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    dragStartX = x;
    dragStartY = y;
    cropStartData = { ...cropData };

    // Check if clicking on resize handle
    resizeHandle = getResizeHandle(x, y, canvas);

    if (resizeHandle) {
        isResizing = true;
    } else if (isInsideCropArea(x, y, canvas)) {
        isDragging = true;
    } else {
        // Start new crop area
        const scaleX = videoDimensions.width / canvas.width;
        const scaleY = videoDimensions.height / canvas.height;

        cropData.x = Math.floor(x * scaleX);
        cropData.y = Math.floor(y * scaleY);
        cropData.width = 100;
        cropData.height = 100;

        drawCropArea();
        updateCropValues();
    }
}

function onMouseMove(e) {
    if (!isDragging && !isResizing) return;

    const canvas = e.target;
    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    const deltaX = x - dragStartX;
    const deltaY = y - dragStartY;

    const scaleX = videoDimensions.width / canvas.width;
    const scaleY = videoDimensions.height / canvas.height;

    if (isDragging) {
        // Move crop area
        cropData.x = Math.max(0, Math.min(
            videoDimensions.width - cropData.width,
            cropStartData.x + Math.floor(deltaX * scaleX)
        ));
        cropData.y = Math.max(0, Math.min(
            videoDimensions.height - cropData.height,
            cropStartData.y + Math.floor(deltaY * scaleY)
        ));
    } else if (isResizing) {
        // Resize crop area
        resizeCropArea(deltaX * scaleX, deltaY * scaleY);
    }

    drawCropArea();
    updateCropValues();
}

function onMouseUp() {
    isDragging = false;
    isResizing = false;
    resizeHandle = null;
    cropStartData = null;
}

// Helper functions
function getResizeHandle(x, y, canvas) {
    const scaleX = canvas.width / videoDimensions.width;
    const scaleY = canvas.height / videoDimensions.height;

    const cropX = cropData.x * scaleX;
    const cropY = cropData.y * scaleY;
    const cropWidth = cropData.width * scaleX;
    const cropHeight = cropData.height * scaleY;

    const handleSize = 8;
    const tolerance = handleSize / 2;

    // Define handle positions
    const handles = [
        { name: 'nw', x: cropX, y: cropY },
        { name: 'n', x: cropX + cropWidth/2, y: cropY },
        { name: 'ne', x: cropX + cropWidth, y: cropY },
        { name: 'e', x: cropX + cropWidth, y: cropY + cropHeight/2 },
        { name: 'se', x: cropX + cropWidth, y: cropY + cropHeight },
        { name: 's', x: cropX + cropWidth/2, y: cropY + cropHeight },
        { name: 'sw', x: cropX, y: cropY + cropHeight },
        { name: 'w', x: cropX, y: cropY + cropHeight/2 }
    ];

    // Check if mouse is near any handle
    for (const handle of handles) {
        if (Math.abs(x - handle.x) <= tolerance && Math.abs(y - handle.y) <= tolerance) {
            return handle.name;
        }
    }

    return null;
}

function isInsideCropArea(x, y, canvas) {
    const scaleX = canvas.width / videoDimensions.width;
    const scaleY = canvas.height / videoDimensions.height;

    const cropX = cropData.x * scaleX;
    const cropY = cropData.y * scaleY;
    const cropWidth = cropData.width * scaleX;
    const cropHeight = cropData.height * scaleY;

    return x >= cropX && x <= cropX + cropWidth &&
           y >= cropY && y <= cropY + cropHeight;
}

function resizeCropArea(deltaX, deltaY) {
    const minSize = 50;
    let newX = cropStartData.x;
    let newY = cropStartData.y;
    let newWidth = cropStartData.width;
    let newHeight = cropStartData.height;

    switch (resizeHandle) {
        case 'nw':
            newX = cropStartData.x + deltaX;
            newY = cropStartData.y + deltaY;
            newWidth = cropStartData.width - deltaX;
            newHeight = cropStartData.height - deltaY;
            break;
        case 'n':
            newY = cropStartData.y + deltaY;
            newHeight = cropStartData.height - deltaY;
            break;
        case 'ne':
            newY = cropStartData.y + deltaY;
            newWidth = cropStartData.width + deltaX;
            newHeight = cropStartData.height - deltaY;
            break;
        case 'e':
            newWidth = cropStartData.width + deltaX;
            break;
        case 'se':
            newWidth = cropStartData.width + deltaX;
            newHeight = cropStartData.height + deltaY;
            break;
        case 's':
            newHeight = cropStartData.height + deltaY;
            break;
        case 'sw':
            newX = cropStartData.x + deltaX;
            newWidth = cropStartData.width - deltaX;
            newHeight = cropStartData.height + deltaY;
            break;
        case 'w':
            newX = cropStartData.x + deltaX;
            newWidth = cropStartData.width - deltaX;
            break;
    }

    // Apply constraints
    if (newWidth < minSize) {
        if (resizeHandle.includes('w')) newX = cropStartData.x + cropStartData.width - minSize;
        newWidth = minSize;
    }
    if (newHeight < minSize) {
        if (resizeHandle.includes('n')) newY = cropStartData.y + cropStartData.height - minSize;
        newHeight = minSize;
    }

    // Keep within bounds
    newX = Math.max(0, Math.min(videoDimensions.width - newWidth, newX));
    newY = Math.max(0, Math.min(videoDimensions.height - newHeight, newY));
    newWidth = Math.min(videoDimensions.width - newX, newWidth);
    newHeight = Math.min(videoDimensions.height - newY, newHeight);

    cropData.x = Math.floor(newX);
    cropData.y = Math.floor(newY);
    cropData.width = Math.floor(newWidth);
    cropData.height = Math.floor(newHeight);
}

// Draw crop area on canvas
function drawCropArea() {
    const canvas = document.getElementById('cropCanvas');
    const ctx = canvas.getContext('2d');

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Convert video coordinates to canvas coordinates
    const scaleX = canvas.width / videoDimensions.width;
    const scaleY = canvas.height / videoDimensions.height;

    const x = cropData.x * scaleX;
    const y = cropData.y * scaleY;
    const width = cropData.width * scaleX;
    const height = cropData.height * scaleY;

    // Draw semi-transparent overlay
    ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Clear crop area
    ctx.clearRect(x, y, width, height);

    // Draw crop border
    ctx.strokeStyle = '#00ff00';
    ctx.lineWidth = 2;
    ctx.strokeRect(x, y, width, height);

    // Draw resize handles
    const handleSize = 8;
    const handles = [
        { x: x, y: y }, // nw
        { x: x + width/2, y: y }, // n
        { x: x + width, y: y }, // ne
        { x: x + width, y: y + height/2 }, // e
        { x: x + width, y: y + height }, // se
        { x: x + width/2, y: y + height }, // s
        { x: x, y: y + height }, // sw
        { x: x, y: y + height/2 } // w
    ];

    ctx.fillStyle = '#00ff00';
    ctx.strokeStyle = '#ffffff';
    ctx.lineWidth = 1;

    handles.forEach(handle => {
        ctx.fillRect(handle.x - handleSize/2, handle.y - handleSize/2, handleSize, handleSize);
        ctx.strokeRect(handle.x - handleSize/2, handle.y - handleSize/2, handleSize, handleSize);
    });
}

function updateCropValues() {
    document.getElementById('cropX').textContent = cropData.x;
    document.getElementById('cropY').textContent = cropData.y;
    document.getElementById('cropWidth').textContent = cropData.width;
    document.getElementById('cropHeight').textContent = cropData.height;
}

// Process crop and create final video
async function processCrop() {
    const doctorName = document.getElementById('doctorName').value.trim();
    
    if (!uploadedFilename || !doctorName) {
        alert('Missing required data');
        return;
    }
    
    console.log('Processing crop:', cropData);
    
    // Move to processing step
    currentStep = 4;
    showStep(currentStep);
    updateProgress(currentStep);
    
    // Animate processing steps
    setTimeout(() => updateProcessingStep(2), 1000);
    setTimeout(() => updateProcessingStep(3), 3000);
    setTimeout(() => updateProcessingStep(4), 5000);
    
    try {
        const response = await fetch('/digilabs/v2/videoframmer/crop', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                filename: uploadedFilename,
                cropData: cropData,
                doctorName: doctorName
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            finalVideoFilename = result.finalVideo;
            showFinalVideo();
        } else {
            alert('Processing failed: ' + result.error);
        }
    } catch (error) {
        alert('Processing error: ' + error.message);
    }
}

function updateProcessingStep(step) {
    const stepElement = document.getElementById(`processStep${step}`);
    stepElement.innerHTML = stepElement.innerHTML.replace('⏳', '✓');
    stepElement.style.color = '#28a745';
}

function showFinalVideo() {
    currentStep = 5;
    showStep(currentStep);
    
    const finalVideo = document.getElementById('finalVideo');
    finalVideo.src = `/uploads/${finalVideoFilename}`;
}

// Download video
function downloadVideo() {
    if (finalVideoFilename) {
        window.open(`/digilabs/v2/videoframmer/download/${finalVideoFilename}`, '_blank');
    }
}

// Start over
function startOver() {
    currentStep = 1;
    showStep(currentStep);
    updateProgress(currentStep);
    
    // Reset form
    document.getElementById('doctorName').value = '';
    document.getElementById('videoFile').value = '';
    uploadedFilename = '';
    finalVideoFilename = '';
}

// Initialize
document.addEventListener('DOMContentLoaded', () => {
    updateProgress(1);
});
