let currentStep = 1;
let uploadedFilename = '';
let videoDimensions = { width: 0, height: 0 };
let cropData = { x: 0, y: 0, width: 200, height: 200 };
let isDragging = false;
let isResizing = false;
let dragStart = { x: 0, y: 0 };
let finalVideoFilename = '';

// Progress management
function updateProgress(step) {
    const progressFill = document.getElementById('progressFill');
    const progressPercentage = (step / 4) * 100;
    progressFill.style.width = progressPercentage + '%';
    
    // Update step indicators
    for (let i = 1; i <= 4; i++) {
        const stepElement = document.getElementById(`step${i}`);
        stepElement.classList.remove('active', 'completed');
        
        if (i < step) {
            stepElement.classList.add('completed');
        } else if (i === step) {
            stepElement.classList.add('active');
        }
    }
}

// Step navigation
function nextStep(fromStep) {
    if (fromStep === 1) {
        const doctorName = document.getElementById('doctorName').value.trim();
        if (!doctorName) {
            alert('Please enter doctor name');
            return;
        }
    }
    
    currentStep = fromStep + 1;
    showStep(currentStep);
    updateProgress(currentStep);
}

function showStep(step) {
    // Hide all step contents
    const stepContents = document.querySelectorAll('.step-content');
    stepContents.forEach(content => content.classList.remove('active'));
    
    // Show current step
    document.getElementById(`stepContent${step}`).classList.add('active');
}

// Video upload and processing
async function uploadVideo() {
    const fileInput = document.getElementById('videoFile');
    const file = fileInput.files[0];
    
    if (!file) {
        alert('Please select a video file');
        return;
    }
    
    const uploadBtn = document.getElementById('uploadBtn');
    uploadBtn.textContent = 'Uploading...';
    uploadBtn.disabled = true;
    
    const formData = new FormData();
    formData.append('video', file);
    
    try {
        const response = await fetch('/api/upload', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            uploadedFilename = result.filename;
            videoDimensions = result.dimensions;
            
            // Initialize crop area
            cropData = {
                x: 0,
                y: 0,
                width: Math.min(videoDimensions.width, 400),
                height: Math.min(videoDimensions.height, 400)
            };
            
            setupCropInterface();
            nextStep(2);
        } else {
            alert('Upload failed: ' + result.error);
        }
    } catch (error) {
        alert('Upload error: ' + error.message);
    }
    
    uploadBtn.textContent = 'Upload & Crop';
    uploadBtn.disabled = false;
}

// Setup crop interface
function setupCropInterface() {
    const video = document.getElementById('videoPreview');
    const canvas = document.getElementById('cropCanvas');
    const ctx = canvas.getContext('2d');
    
    video.src = `/uploads/${uploadedFilename}`;
    
    video.onloadedmetadata = () => {
        const rect = video.getBoundingClientRect();
        canvas.width = rect.width;
        canvas.height = rect.height;
        
        // Position canvas over video
        canvas.style.width = rect.width + 'px';
        canvas.style.height = rect.height + 'px';
        
        drawCropOverlay();
        updateCropValues();
    };
    
    // Mouse events for cropping
    canvas.addEventListener('mousedown', startCrop);
    canvas.addEventListener('mousemove', updateCrop);
    canvas.addEventListener('mouseup', endCrop);
    
    // Prevent context menu
    canvas.addEventListener('contextmenu', e => e.preventDefault());
}

function drawCropOverlay() {
    const canvas = document.getElementById('cropCanvas');
    const ctx = canvas.getContext('2d');
    const video = document.getElementById('videoPreview');
    
    if (!video || !canvas) return;
    
    const rect = video.getBoundingClientRect();
    const scaleX = rect.width / videoDimensions.width;
    const scaleY = rect.height / videoDimensions.height;
    
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Draw semi-transparent overlay
    ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // Calculate crop area in canvas coordinates
    const cropX = cropData.x * scaleX;
    const cropY = cropData.y * scaleY;
    const cropWidth = cropData.width * scaleX;
    const cropHeight = cropData.height * scaleY;
    
    // Clear crop area (make it transparent)
    ctx.clearRect(cropX, cropY, cropWidth, cropHeight);
    
    // Draw crop border
    ctx.strokeStyle = '#00ff00';
    ctx.lineWidth = 2;
    ctx.strokeRect(cropX, cropY, cropWidth, cropHeight);
    
    // Draw resize handles
    const handleSize = 8;
    ctx.fillStyle = '#00ff00';
    
    // Corner handles
    ctx.fillRect(cropX - handleSize/2, cropY - handleSize/2, handleSize, handleSize);
    ctx.fillRect(cropX + cropWidth - handleSize/2, cropY - handleSize/2, handleSize, handleSize);
    ctx.fillRect(cropX - handleSize/2, cropY + cropHeight - handleSize/2, handleSize, handleSize);
    ctx.fillRect(cropX + cropWidth - handleSize/2, cropY + cropHeight - handleSize/2, handleSize, handleSize);
}

function startCrop(e) {
    const rect = e.target.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    dragStart = { x, y };
    
    // Check if clicking on resize handle or crop area
    const video = document.getElementById('videoPreview');
    const videoRect = video.getBoundingClientRect();
    const scaleX = videoRect.width / videoDimensions.width;
    const scaleY = videoRect.height / videoDimensions.height;
    
    const cropX = cropData.x * scaleX;
    const cropY = cropData.y * scaleY;
    const cropWidth = cropData.width * scaleX;
    const cropHeight = cropData.height * scaleY;
    
    // Check if clicking inside crop area
    if (x >= cropX && x <= cropX + cropWidth && y >= cropY && y <= cropY + cropHeight) {
        isDragging = true;
        e.target.style.cursor = 'move';
    }
}

function updateCrop(e) {
    if (!isDragging) return;
    
    const rect = e.target.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    const deltaX = x - dragStart.x;
    const deltaY = y - dragStart.y;
    
    const video = document.getElementById('videoPreview');
    const videoRect = video.getBoundingClientRect();
    const scaleX = videoDimensions.width / videoRect.width;
    const scaleY = videoDimensions.height / videoRect.height;
    
    // Update crop position
    cropData.x = Math.max(0, Math.min(videoDimensions.width - cropData.width, 
        cropData.x + deltaX * scaleX));
    cropData.y = Math.max(0, Math.min(videoDimensions.height - cropData.height, 
        cropData.y + deltaY * scaleY));
    
    dragStart = { x, y };
    drawCropOverlay();
    updateCropValues();
}

function endCrop(e) {
    isDragging = false;
    isResizing = false;
    e.target.style.cursor = 'crosshair';
}

function updateCropValues() {
    document.getElementById('cropX').textContent = Math.round(cropData.x);
    document.getElementById('cropY').textContent = Math.round(cropData.y);
    document.getElementById('cropWidth').textContent = Math.round(cropData.width);
    document.getElementById('cropHeight').textContent = Math.round(cropData.height);
}

// Process crop and create final video
async function processCrop() {
    const doctorName = document.getElementById('doctorName').value.trim();
    
    if (!uploadedFilename || !doctorName) {
        alert('Missing required data');
        return;
    }
    
    // Move to processing step
    currentStep = 4;
    showStep(currentStep);
    updateProgress(currentStep);
    
    // Animate processing steps
    setTimeout(() => updateProcessingStep(2), 1000);
    setTimeout(() => updateProcessingStep(3), 3000);
    setTimeout(() => updateProcessingStep(4), 5000);
    
    try {
        const response = await fetch('/api/crop', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                filename: uploadedFilename,
                cropData: cropData,
                doctorName: doctorName
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            finalVideoFilename = result.finalVideo;
            showFinalVideo();
        } else {
            alert('Processing failed: ' + result.error);
        }
    } catch (error) {
        alert('Processing error: ' + error.message);
    }
}

function updateProcessingStep(step) {
    const stepElement = document.getElementById(`processStep${step}`);
    stepElement.innerHTML = stepElement.innerHTML.replace('⏳', '✓');
    stepElement.style.color = '#28a745';
}

function showFinalVideo() {
    currentStep = 5;
    showStep(currentStep);
    
    const finalVideo = document.getElementById('finalVideo');
    finalVideo.src = `/uploads/${finalVideoFilename}`;
}

// Download video
function downloadVideo() {
    if (finalVideoFilename) {
        window.open(`/api/download/${finalVideoFilename}`, '_blank');
    }
}

// Start over
function startOver() {
    currentStep = 1;
    showStep(currentStep);
    updateProgress(currentStep);
    
    // Reset form
    document.getElementById('doctorName').value = '';
    document.getElementById('videoFile').value = '';
    uploadedFilename = '';
    finalVideoFilename = '';
}

// Initialize
document.addEventListener('DOMContentLoaded', () => {
    updateProgress(1);
});
