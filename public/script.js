let currentStep = 1;
let uploadedFilename = '';
let videoDimensions = { width: 0, height: 0 };
let cropData = { x: 0, y: 0, width: 200, height: 200 };
let finalVideoFilename = '';

// Cropping variables
let isDrawing = false;
let startX, startY;

// Progress management
function updateProgress(step) {
    const progressFill = document.getElementById('progressFill');
    const progressPercentage = (step / 4) * 100;
    progressFill.style.width = progressPercentage + '%';
    
    // Update step indicators
    for (let i = 1; i <= 4; i++) {
        const stepElement = document.getElementById(`step${i}`);
        stepElement.classList.remove('active', 'completed');
        
        if (i < step) {
            stepElement.classList.add('completed');
        } else if (i === step) {
            stepElement.classList.add('active');
        }
    }
}

// Step navigation
function nextStep(fromStep) {
    if (fromStep === 1) {
        const doctorName = document.getElementById('doctorName').value.trim();
        if (!doctorName) {
            alert('Please enter doctor name');
            return;
        }
    }
    
    currentStep = fromStep + 1;
    showStep(currentStep);
    updateProgress(currentStep);
}

function showStep(step) {
    // Hide all step contents
    const stepContents = document.querySelectorAll('.step-content');
    stepContents.forEach(content => content.classList.remove('active'));
    
    // Show current step
    document.getElementById(`stepContent${step}`).classList.add('active');
}

// Video upload
async function uploadVideo() {
    const fileInput = document.getElementById('videoFile');
    const file = fileInput.files[0];
    
    if (!file) {
        alert('Please select a video file');
        return;
    }
    
    const uploadBtn = document.getElementById('uploadBtn');
    uploadBtn.textContent = 'Uploading...';
    uploadBtn.disabled = true;
    
    const formData = new FormData();
    formData.append('video', file);
    
    try {
        const response = await fetch('/api/upload', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            uploadedFilename = result.filename;
            videoDimensions = result.dimensions;
            
            console.log('Video uploaded:', uploadedFilename, 'Dimensions:', videoDimensions);
            
            setupCropInterface();
            nextStep(2);
        } else {
            alert('Upload failed: ' + result.error);
        }
    } catch (error) {
        alert('Upload error: ' + error.message);
    }
    
    uploadBtn.textContent = 'Upload & Crop';
    uploadBtn.disabled = false;
}

// Setup crop interface
function setupCropInterface() {
    const video = document.getElementById('videoPreview');
    const canvas = document.getElementById('cropCanvas');
    
    video.src = `/uploads/${uploadedFilename}`;
    
    video.onloadedmetadata = () => {
        const container = video.parentElement;
        const containerRect = container.getBoundingClientRect();
        
        // Set video size to fit container
        const aspectRatio = videoDimensions.width / videoDimensions.height;
        let displayWidth = Math.min(containerRect.width - 40, 600);
        let displayHeight = displayWidth / aspectRatio;
        
        if (displayHeight > 400) {
            displayHeight = 400;
            displayWidth = displayHeight * aspectRatio;
        }
        
        video.style.width = displayWidth + 'px';
        video.style.height = displayHeight + 'px';
        
        // Set canvas to match video display size
        canvas.width = displayWidth;
        canvas.height = displayHeight;
        canvas.style.width = displayWidth + 'px';
        canvas.style.height = displayHeight + 'px';
        
        // Initialize crop area (center of video)
        cropData = {
            x: Math.floor(videoDimensions.width * 0.25),
            y: Math.floor(videoDimensions.height * 0.25),
            width: Math.floor(videoDimensions.width * 0.5),
            height: Math.floor(videoDimensions.height * 0.5)
        };
        
        setupCanvasEvents();
        drawCropArea();
        updateCropValues();
    };
}

// Setup canvas mouse events
function setupCanvasEvents() {
    const canvas = document.getElementById('cropCanvas');
    
    canvas.addEventListener('mousedown', startCrop);
    canvas.addEventListener('mousemove', updateCrop);
    canvas.addEventListener('mouseup', endCrop);
    canvas.addEventListener('mouseleave', endCrop);
}

function startCrop(e) {
    const rect = e.target.getBoundingClientRect();
    startX = e.clientX - rect.left;
    startY = e.clientY - rect.top;
    isDrawing = true;
    
    // Convert canvas coordinates to video coordinates
    const scaleX = videoDimensions.width / e.target.width;
    const scaleY = videoDimensions.height / e.target.height;
    
    cropData.x = Math.floor(startX * scaleX);
    cropData.y = Math.floor(startY * scaleY);
    cropData.width = 50;
    cropData.height = 50;
    
    drawCropArea();
    updateCropValues();
}

function updateCrop(e) {
    if (!isDrawing) return;
    
    const rect = e.target.getBoundingClientRect();
    const currentX = e.clientX - rect.left;
    const currentY = e.clientY - rect.top;
    
    // Convert canvas coordinates to video coordinates
    const scaleX = videoDimensions.width / e.target.width;
    const scaleY = videoDimensions.height / e.target.height;
    
    const width = Math.abs(currentX - startX) * scaleX;
    const height = Math.abs(currentY - startY) * scaleY;
    
    cropData.x = Math.floor(Math.min(startX, currentX) * scaleX);
    cropData.y = Math.floor(Math.min(startY, currentY) * scaleY);
    cropData.width = Math.floor(Math.max(50, width));
    cropData.height = Math.floor(Math.max(50, height));
    
    // Keep crop within video bounds
    cropData.x = Math.max(0, Math.min(videoDimensions.width - cropData.width, cropData.x));
    cropData.y = Math.max(0, Math.min(videoDimensions.height - cropData.height, cropData.y));
    
    drawCropArea();
    updateCropValues();
}

function endCrop(e) {
    isDrawing = false;
}

// Draw crop area on canvas
function drawCropArea() {
    const canvas = document.getElementById('cropCanvas');
    const ctx = canvas.getContext('2d');
    
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Convert video coordinates to canvas coordinates
    const scaleX = canvas.width / videoDimensions.width;
    const scaleY = canvas.height / videoDimensions.height;
    
    const x = cropData.x * scaleX;
    const y = cropData.y * scaleY;
    const width = cropData.width * scaleX;
    const height = cropData.height * scaleY;
    
    // Draw semi-transparent overlay
    ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // Clear crop area
    ctx.clearRect(x, y, width, height);
    
    // Draw crop border
    ctx.strokeStyle = '#00ff00';
    ctx.lineWidth = 2;
    ctx.strokeRect(x, y, width, height);
}

function updateCropValues() {
    document.getElementById('cropX').textContent = cropData.x;
    document.getElementById('cropY').textContent = cropData.y;
    document.getElementById('cropWidth').textContent = cropData.width;
    document.getElementById('cropHeight').textContent = cropData.height;
}

// Process crop and create final video
async function processCrop() {
    const doctorName = document.getElementById('doctorName').value.trim();
    
    if (!uploadedFilename || !doctorName) {
        alert('Missing required data');
        return;
    }
    
    console.log('Processing crop:', cropData);
    
    // Move to processing step
    currentStep = 4;
    showStep(currentStep);
    updateProgress(currentStep);
    
    // Animate processing steps
    setTimeout(() => updateProcessingStep(2), 1000);
    setTimeout(() => updateProcessingStep(3), 3000);
    setTimeout(() => updateProcessingStep(4), 5000);
    
    try {
        const response = await fetch('/api/crop', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                filename: uploadedFilename,
                cropData: cropData,
                doctorName: doctorName
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            finalVideoFilename = result.finalVideo;
            showFinalVideo();
        } else {
            alert('Processing failed: ' + result.error);
        }
    } catch (error) {
        alert('Processing error: ' + error.message);
    }
}

function updateProcessingStep(step) {
    const stepElement = document.getElementById(`processStep${step}`);
    stepElement.innerHTML = stepElement.innerHTML.replace('⏳', '✓');
    stepElement.style.color = '#28a745';
}

function showFinalVideo() {
    currentStep = 5;
    showStep(currentStep);
    
    const finalVideo = document.getElementById('finalVideo');
    finalVideo.src = `/uploads/${finalVideoFilename}`;
}

// Download video
function downloadVideo() {
    if (finalVideoFilename) {
        window.open(`/api/download/${finalVideoFilename}`, '_blank');
    }
}

// Start over
function startOver() {
    currentStep = 1;
    showStep(currentStep);
    updateProgress(currentStep);
    
    // Reset form
    document.getElementById('doctorName').value = '';
    document.getElementById('videoFile').value = '';
    uploadedFilename = '';
    finalVideoFilename = '';
}

// Initialize
document.addEventListener('DOMContentLoaded', () => {
    updateProgress(1);
});
