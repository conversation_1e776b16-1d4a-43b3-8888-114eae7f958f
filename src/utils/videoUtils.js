// Video utility functions

const validateCropData = (cropData) => {
  // Ensure crop values are valid integers
  const x = Math.max(0, Math.floor(Number(cropData.x) || 0));
  const y = Math.max(0, Math.floor(Number(cropData.y) || 0));
  const width = Math.max(50, Math.floor(Number(cropData.width) || 100));
  const height = Math.max(50, Math.floor(Number(cropData.height) || 100));

  return { x, y, width, height };
};

const getOptimizedVideoSettings = (type = 'crop') => {
  const settings = {
    crop: {
      preset: 'medium',
      crf: '28',
      maxrate: '1M',
      bufsize: '2M',
      profile: 'baseline',
      level: '3.0'
    },
    final: {
      preset: 'medium',
      crf: '26',
      maxrate: '2M',
      bufsize: '4M',
      profile: 'main',
      level: '4.0',
      framerate: '30'
    }
  };

  return settings[type] || settings.crop;
};

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

module.exports = {
  validateCropData,
  getOptimizedVideoSettings,
  formatFileSize
};
