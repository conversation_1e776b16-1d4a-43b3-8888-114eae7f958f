const express = require('express');
const router = express.Router();
const videoController = require('../controllers/videoController');
const uploadMiddleware = require('../middleware/uploadMiddleware');

// Upload video
router.post('/upload', uploadMiddleware.single('video'), videoController.uploadVideo);

// Crop and process video
router.post('/crop', videoController.cropVideo);

// Download video
router.get('/download/:filename', videoController.downloadVideo);

module.exports = router;
