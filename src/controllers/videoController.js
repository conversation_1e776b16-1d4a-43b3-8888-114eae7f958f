const ffmpeg = require('fluent-ffmpeg');
const path = require('path');
const fs = require('fs');
const videoUtils = require('../utils/videoUtils');

// Upload video and get dimensions
const uploadVideo = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No video file uploaded' });
    }

    const videoPath = req.file.path;

    // Get video dimensions
    ffmpeg.ffprobe(videoPath, (err, metadata) => {
      if (err) {
        console.error('Error getting video info:', err);
        return res.status(500).json({ error: 'Failed to process video' });
      }

      const videoStream = metadata.streams.find(stream => stream.codec_type === 'video');
      const dimensions = {
        width: videoStream.width,
        height: videoStream.height,
        duration: metadata.format.duration
      };

      console.log('Video uploaded:', req.file.filename, 'Dimensions:', dimensions);

      res.json({
        success: true,
        filename: req.file.filename,
        dimensions: dimensions
      });
    });
  } catch (error) {
    console.error('Upload error:', error);
    res.status(500).json({ error: 'Upload failed' });
  }
};

// Crop and overlay video with optimized compression
const cropVideo = async (req, res) => {
  try {
    const { filename, cropData, doctorName } = req.body;

    if (!filename || !cropData || !doctorName) {
      return res.status(400).json({ error: 'Missing required data' });
    }

    console.log('Crop request:', { filename, cropData, doctorName });

    const inputPath = path.join('uploads', filename);
    const croppedFilename = 'cropped-' + Date.now() + '.mp4';
    const croppedPath = path.join('uploads', croppedFilename);

    // Validate and prepare crop values
    const cropParams = videoUtils.validateCropData(cropData);
    console.log(`Cropping: ${cropParams.width}x${cropParams.height} at (${cropParams.x},${cropParams.y})`);

    // Step 1: Crop the video with optimized settings for smaller file size
    ffmpeg(inputPath)
      .videoFilters(`crop=${cropParams.width}:${cropParams.height}:${cropParams.x}:${cropParams.y}`, 'scale=300:300')
      .videoCodec('libx264')
      .audioCodec('aac')
      .outputOptions([
        '-preset', 'medium',        // Better compression than 'fast'
        '-crf', '28',              // Higher CRF = smaller file (was 23)
        '-pix_fmt', 'yuv420p',
        '-movflags', '+faststart',  // Web optimization
        '-profile:v', 'baseline',   // Better compatibility
        '-level', '3.0',
        '-maxrate', '1M',          // Max bitrate 1Mbps
        '-bufsize', '2M',          // Buffer size
        '-vf', 'scale=trunc(iw/2)*2:trunc(ih/2)*2' // Ensure even dimensions
      ])
      .output(croppedPath)
      .on('end', () => {
        console.log('Video cropped successfully');

        // Step 2: Overlay on background video
        overlayVideo(croppedPath, croppedFilename, doctorName, res);
      })
      .on('error', (err) => {
        console.error('Crop error:', err);
        res.status(500).json({ error: 'Failed to crop video: ' + err.message });
      })
      .run();

  } catch (error) {
    console.error('Crop video error:', error);
    res.status(500).json({ error: 'Processing failed' });
  }
};

// Overlay function with optimized compression
const overlayVideo = (croppedPath, croppedFilename, doctorName, res) => {
  const backgroundVideo = path.join('uploads', 'Gudi padwa GIF.mp4');
  const finalFilename = 'final-' + Date.now() + '.mp4';
  const finalPath = path.join('uploads', finalFilename);

  // Check if background video exists
  if (!fs.existsSync(backgroundVideo)) {
    return res.status(500).json({ error: 'Background video not found' });
  }

  // Overlay cropped video on background (show from 2s to 6s) with compression
  ffmpeg(backgroundVideo)
    .input(croppedPath)
    .complexFilter([
      '[1:v]setpts=PTS+2/TB[delayed]',
      '[0:v][delayed]overlay=100:100:enable=\'between(t,2,6)\'[v]'
    ])
    .outputOptions([
      '-map', '[v]',
      '-map', '0:a?',
      '-c:v', 'libx264',
      '-c:a', 'aac',
      '-preset', 'medium',        // Better compression
      '-crf', '26',              // Optimized for final output
      '-pix_fmt', 'yuv420p',
      '-movflags', '+faststart',  // Web optimization
      '-profile:v', 'main',       // Good balance of compression and compatibility
      '-level', '4.0',
      '-maxrate', '2M',          // Max bitrate 2Mbps for final video
      '-bufsize', '4M',
      '-r', '30'                 // Limit frame rate to 30fps
    ])
    .output(finalPath)
    .on('end', () => {
      console.log('Final video created successfully');

      // Clean up files after 5 minutes
      setTimeout(() => {
        const filesToClean = [
          path.join('uploads', croppedFilename.replace('cropped-', 'video-')), // original
          croppedPath,
          finalPath
        ];

        filesToClean.forEach(filePath => {
          fs.unlink(filePath, (err) => {
            if (err && err.code !== 'ENOENT') {
              console.error('Cleanup error:', err);
            } else {
              console.log('Cleaned up:', path.basename(filePath));
            }
          });
        });
      }, 5 * 60 * 1000);

      res.json({
        success: true,
        finalVideo: finalFilename,
        message: 'Video processed successfully with optimized compression'
      });
    })
    .on('error', (err) => {
      console.error('Overlay error:', err);
      res.status(500).json({ error: 'Failed to create final video: ' + err.message });
    })
    .run();
};

// Download video
const downloadVideo = (req, res) => {
  try {
    const filename = req.params.filename;
    const filePath = path.join('uploads', filename);

    if (fs.existsSync(filePath)) {
      res.download(filePath, (err) => {
        if (err) {
          console.error('Download error:', err);
          res.status(500).json({ error: 'Download failed' });
        }
      });
    } else {
      res.status(404).json({ error: 'File not found' });
    }
  } catch (error) {
    console.error('Download error:', error);
    res.status(500).json({ error: 'Download failed' });
  }
};

module.exports = {
  uploadVideo,
  cropVideo,
  downloadVideo
};
