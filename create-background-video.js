const ffmpeg = require('fluent-ffmpeg');
const path = require('path');

// Create a simple background video if it doesn't exist
function createBackgroundVideo() {
  const outputPath = path.join('uploads', 'Gudi padwa GIF.mp4');
  
  console.log('Creating background video...');
  
  // Create a simple colored background video (10 seconds, 1080x1920 for vertical format)
  ffmpeg()
    .input('color=c=blue:size=1080x1920:duration=10:rate=30')
    .inputFormat('lavfi')
    .output(outputPath)
    .videoCodec('libx264')
    .audioCodec('aac')
    .outputOptions([
      '-pix_fmt yuv420p',
      '-t 10'
    ])
    .on('end', () => {
      console.log('✅ Background video created successfully!');
      console.log('📁 Location: uploads/Gudi padwa GIF.mp4');
    })
    .on('error', (err) => {
      console.error('❌ Error creating background video:', err.message);
      console.log('💡 Please manually add "Gudi padwa GIF.mp4" to the uploads folder');
    })
    .run();
}

createBackgroundVideo();
