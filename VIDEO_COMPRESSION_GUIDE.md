# Video Compression Settings Guide

## Current Optimized Settings

### Cropped Video Compression
- **CRF**: 28 (Higher = smaller file, was 23)
- **Preset**: medium (Better compression than 'fast')
- **Max Bitrate**: 1Mbps (Limits file size)
- **Buffer Size**: 2MB
- **Profile**: baseline (Better compatibility)
- **Level**: 3.0

### Final Video Compression
- **CRF**: 26 (Optimized for final output)
- **Preset**: medium
- **Max Bitrate**: 2Mbps
- **Buffer Size**: 4MB
- **Profile**: main
- **Level**: 4.0
- **Frame Rate**: 30fps (Limited from higher rates)

## How to Adjust Video Size

### 1. Make Videos Even Smaller
Edit `src/controllers/videoController.js`:

```javascript
// For VERY small files (lower quality):
'-crf', '32',              // Higher CRF = smaller file
'-maxrate', '500k',        // Lower bitrate
'-preset', 'slow',         // Better compression (slower)

// For cropped video:
'-maxrate', '800k',        // Even lower bitrate
'-crf', '30',              // Higher CRF
```

### 2. Better Quality (Larger Files)
```javascript
// For better quality:
'-crf', '23',              // Lower CRF = better quality
'-maxrate', '3M',          // Higher bitrate
'-preset', 'fast',         // Faster encoding

// For final video:
'-maxrate', '4M',          // Higher bitrate
'-crf', '22',              // Better quality
```

### 3. Resolution Scaling
Add resolution scaling to make files smaller:

```javascript
// Add to video filters:
'-vf', 'scale=720:-2'      // Scale to 720p width
'-vf', 'scale=480:-2'      // Scale to 480p width
'-vf', 'scale=iw/2:ih/2'   // Scale to half size
```

## CRF Values Guide
- **18-23**: High quality, larger files
- **24-26**: Good quality, medium files (current final)
- **27-30**: Lower quality, smaller files (current crop)
- **31-35**: Very small files, noticeable quality loss

## Bitrate Guide
- **500k-1M**: Very small files
- **1M-2M**: Small to medium files (current)
- **2M-4M**: Medium to large files
- **4M+**: Large files, high quality

## File Locations to Edit

### Main Compression Settings
- **File**: `src/controllers/videoController.js`
- **Lines**: 65-75 (crop settings), 115-125 (final settings)

### Utility Settings
- **File**: `src/utils/videoUtils.js`
- **Function**: `getOptimizedVideoSettings()`

## Quick Size Reduction Tips

1. **Increase CRF** (28 → 32) for smaller files
2. **Lower bitrate** (1M → 500k) for crop, (2M → 1M) for final
3. **Add resolution scaling** to reduce dimensions
4. **Use 'slow' preset** for better compression (takes longer)
5. **Limit frame rate** to 24fps instead of 30fps

## Current File Size Expectations
- **Original**: Variable (user upload)
- **Cropped**: ~50-70% smaller than original
- **Final**: ~60-80% of background video size

The current settings prioritize small file sizes while maintaining acceptable quality for web delivery.
