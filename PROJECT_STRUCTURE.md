# Video Frammer - Project Structure

## 📁 Folder Structure

```
Video_Frammer_Augment/
├── src/                          # Source code (NEW)
│   ├── app.js                   # Express app configuration
│   ├── controllers/             # Business logic
│   │   └── videoController.js   # Video processing logic
│   ├── routes/                  # API routes
│   │   └── videoRoutes.js       # Video-related routes
│   ├── middleware/              # Custom middleware
│   │   └── uploadMiddleware.js  # File upload handling
│   ├── utils/                   # Utility functions
│   │   └── videoUtils.js        # Video processing utilities
│   └── config/                  # Configuration files (future)
├── public/                      # Frontend files
│   ├── index.html              # Main HTML page
│   ├── style.css               # Styling
│   └── script.js               # Frontend JavaScript
├── uploads/                     # File storage
│   └── Gudi padwa GIF.mp4      # Background video
├── server-new.js               # Main server entry point (NEW)
├── server.js                   # Old server (backup)
├── package.json                # Dependencies
├── VIDEO_COMPRESSION_GUIDE.md  # Compression settings guide
└── PROJECT_STRUCTURE.md        # This file
```

## 🚀 API Endpoints

### Base URL: `/digilabs/v2/videoframmer`

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/health` | Health check |
| POST | `/upload` | Upload video file |
| POST | `/crop` | Crop and process video |
| GET | `/download/:filename` | Download processed video |

## 📋 API Usage Examples

### 1. Health Check
```bash
curl http://localhost:3000/digilabs/v2/videoframmer/health
```

### 2. Upload Video
```bash
curl -X POST \
  -F "video=@your-video.mp4" \
  http://localhost:3000/digilabs/v2/videoframmer/upload
```

### 3. Crop Video
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{
    "filename": "video-123456789.mp4",
    "cropData": {"x": 100, "y": 100, "width": 500, "height": 500},
    "doctorName": "Dr. Smith"
  }' \
  http://localhost:3000/digilabs/v2/videoframmer/crop
```

### 4. Download Video
```bash
curl -O http://localhost:3000/digilabs/v2/videoframmer/download/final-123456789.mp4
```

## 🔧 Key Features

### Video Compression Optimization
- **Smaller file sizes** with optimized FFmpeg settings
- **CRF 28** for cropped videos (smaller files)
- **CRF 26** for final videos (balanced quality/size)
- **Bitrate limiting** (1Mbps crop, 2Mbps final)
- **Web optimization** with faststart flag

### Professional Folder Structure
- **Separation of concerns** with controllers, routes, middleware
- **Modular design** for easy maintenance
- **Utility functions** for reusable code
- **Clean API structure** with versioned endpoints

### Enhanced Cropping
- **Edge and corner resizing** with 8-point handles
- **Drag to move** functionality
- **Visual feedback** with smart cursors
- **Boundary constraints** and minimum size protection

## 🛠 Development Commands

```bash
# Start production server
npm start

# Start development server
npm run dev

# Test API health
curl http://localhost:3000/digilabs/v2/videoframmer/health
```

## 📊 File Size Optimization

The new compression settings typically produce:
- **Cropped videos**: 50-70% smaller than original
- **Final videos**: 60-80% of background video size
- **Web-optimized**: Fast loading with progressive download

## 🔄 Migration from Old Structure

### Old vs New
- **Old**: Single `server.js` file with all logic
- **New**: Modular structure with separated concerns
- **Old**: `/api/*` routes
- **New**: `/digilabs/v2/videoframmer/*` routes

### Backward Compatibility
- Old `server.js` is preserved as backup
- Frontend updated to use new API routes
- Same functionality with better organization

## 📝 Configuration

### Video Settings
Edit `src/controllers/videoController.js` for compression settings:
- Lines 65-75: Crop video settings
- Lines 115-125: Final video settings

### Route Changes
Edit `src/routes/videoRoutes.js` for API route modifications

### Utilities
Edit `src/utils/videoUtils.js` for helper functions and validation

This structure provides better maintainability, scalability, and follows Node.js best practices.
